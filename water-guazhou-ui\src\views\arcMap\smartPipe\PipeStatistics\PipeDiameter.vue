<!-- 按口径统计管长 -->
<template>
  <RightDrawerMap
    ref="refMap"
    :title="'按口径统计管长'"
    :full-content="true"
    @map-loaded="onMapLoaded"
  >
    <Form
      ref="refForm"
      :config="FormConfig"
    ></Form>
    <template #detail-header>
      <span>统计结果</span>
    </template>
    <template #detail-default>
      <StatisticsCharts
        ref="refStatisticsCharts"
        :view="staticState.view"
        :layer-ids="state.layerIds"
        :all-device="true"
        :query-params="{
          where: '1=1',
          geometry: staticState.graphics?.geometry
        }"
        :statistics-params="{
          group_fields: ['管径'],
          statistic_field: EStatisticField.ShapeLen,
          statistic_type: '2'
        }"
        :tabs="state.tabs"
        :prefix="'DN'"
        :unit="'m'"
        @detail-refreshed="state.loading = false"
        @attr-row-click="handleAttrRowClick"
        @ring-click="handleChartClick"
        @bar-click="handleChartClick"
        @total-row-click="handleTotalRowClick"
      ></StatisticsCharts>
    </template>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import { queryLayerClassName } from '@/api/mapservice'
import { useSketch } from '@/hooks/arcgis'
import { EStatisticField, getGraphicLayer, getLayerOids, getSubLayerIds } from '@/utils/MapHelper'
import { SLMessage } from '@/utils/Message'
// @ts-ignore
import RightDrawerMap from '../../components/common/RightDrawerMap.vue'
// @ts-ignore
import StatisticsCharts from '../../components/components/StatisticsCharts.vue'
import { QueryByPolygon } from '@/utils/geoserver/wfsUtils'

const refMap = ref<InstanceType<typeof RightDrawerMap>>()
const refForm = ref<IFormIns>()
const refStatisticsCharts = ref<InstanceType<typeof StatisticsCharts>>()
const state = reactive<{
  tabs: any[]
  loading: boolean
  layerInfos: any[]
  layerIds: any[]
}>({
  tabs: [],
  layerInfos: [],
  layerIds: [],
  loading: false
})
const staticState: {
  view?: __esri.MapView
  graphics?: __esri.Graphic
  graphicsLayer?: __esri.GraphicsLayer
  sketch?: __esri.SketchViewModel
} = {}
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fieldset: {
        desc: '绘制工具'
      },
      fields: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '绘制多边形',
              disabled: () => state.loading,
              iconifyIcon: 'mdi:shape-polygon-plus',
              styles: {
                flex: '1',
                minWidth: '0'
              },
              click: () => initDraw('polygon')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '绘制矩形',
              disabled: () => state.loading,
              iconifyIcon: 'ep:crop',
              styles: {
                flex: '1',
                minWidth: '0'
              },
              click: () => initDraw('rectangle')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '绘制圆形',
              disabled: () => state.loading,
              iconifyIcon: 'mdi:ellipse-outline',
              styles: {
                flex: '1',
                minWidth: '0'
              },
              click: () => initDraw('circle')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '清除图形',
              disabled: () => state.loading,
              iconifyIcon: 'ep:delete',
              styles: {
                flex: '1',
                minWidth: '0'
              },
              click: () => clearGraphicsLayer()
            }
          ]
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '统计',
              styles: {
                width: '48%'
              },
              loading: () => state.loading,
              click: () => startQuery()
            },
            {
              perm: true,
              text: '导出',
              type: 'primary',
              styles: {
                width: '48%'
              },
              disabled: () => state.loading || state.tabs.length === 0,
              iconifyIcon: 'ep:download',
              click: () => exportStatistics()
            }
          ]
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12
})
const handleTotalRowClick = (row?: any, tab?: string) => {
  const diameter = row.label
  refreshDetailTable(diameter, tab, true)
}
const handleAttrRowClick = (row?: any, tab?: string) => {
  const diameter = row.label
  refreshDetailTable(diameter, tab)
}
const handleChartClick = (params?: any, tab?: string) => {
  const diameter = params.name
  refreshDetailTable(diameter, tab)
}
const refreshDetailTable = async (diameter?: any, tab?: string, showAll?: boolean) => {
  if (diameter === undefined) return
  const d = diameter.replace(/[a-z][A-Z]/gi, '')
  let sql = ''
  if (d === '合计') {
    sql = '1=1'
  } else {
    sql = d === '--' ? 'DIAMETER is null' : "DIAMETER='" + d + "'"
  }
  await startQuery(sql, tab)
  const curTab = refStatisticsCharts.value?.getCurLayer()
  // refStatistic.value?.refreshDetail(curTab, true)
  refStatisticsCharts.value?.refreshDetail(curTab, showAll ?? true)
}
const initDraw = (type: any) => {
  clearGraphicsLayer()
  staticState.sketch?.create(type)
}
const clearGraphicsLayer = () => {
  staticState.graphicsLayer?.removeAll()
  staticState.graphics = undefined
}

const getLayerInfo = async () => {
  state.layerIds = getSubLayerIds(staticState.view)
  const layerInfo = await queryLayerClassName(state.layerIds)
  state.layerInfos = layerInfo.data?.result?.rows || []
  state.layerIds = state.layerInfos
    .filter(item => item.geometrytype === 'esriGeometryPolyline')
    .map(item => item.layerid)
}

const startQuery = async (sql?: string, tab?: string) => {
  SLMessage.info('正在统计，请稍候...')
  const LAYER_NAME = '管线' // 根据实际图层名调整
  try {
    state.loading = true
    debugger
    const allResults: any[] = []
    try {
      // 使用GeoServer的QueryByPolygon方法查询
      const result = await QueryByPolygon(LAYER_NAME, staticState.graphics?.geometry?.rings[0], sql || '1=1')
      
      if (result.data?.features) {
        // 处理要素数据，添加必要的属性
        const processedFeatures = result.data.features.map((feature: any, index: number) => ({
          ...feature.properties,
          OBJECTID: feature.properties.OBJECTID || feature.properties.fid || index + 1,
          geometry: feature.geometry
        }))
        
        allResults.push({
          label:LAYER_NAME,
          name: LAYER_NAME,
          data: processedFeatures // 传递原始要素数据，而不是统计后的数据
        })
      }
    } catch (error) {
      console.warn(`查询图层 ${LAYER_NAME} 失败:`, error)
    }

    if (tab !== undefined) {
      // 更新指定tab的数据
      if (state.tabs.length) {
        const tabObj = state.tabs.find(item => item.name === tab)
        const newTab = allResults.find(item => item.name === tab)
        if (tabObj && newTab) {
          tabObj.data = newTab.data
          refStatisticsCharts.value?.refreshTable(tab, newTab.data)
        }
      } else {
        state.tabs = allResults
      }
    } else {
      state.tabs = allResults
    }
    
    const opend = refMap.value?.isCustomOpened()
    if (!opend) {
      refMap.value?.toggleCustomDetail(true)
    }
    
    // 等待组件渲染完成后再刷新图表
    await nextTick()
    
    // 延迟刷新，确保StatisticsCharts组件完全初始化
    setTimeout(() => {
      if (refStatisticsCharts.value) {
        refStatisticsCharts.value.refreshChar()
      }
    }, 500)
    
  } catch (error) {
    console.log(error)
    state.loading = false
    SLMessage.error('统计失败')
  }
  state.loading = false
}

// 导出统计结果
const exportStatistics = async () => {
  try {
    if (state.tabs.length === 0) {
      SLMessage.warning('暂无统计数据可导出')
      return
    }

    // 动态导入xlsx库
    let XLSX: any
    try {
      XLSX = await import('xlsx')
    } catch (error) {
      SLMessage.error('请先安装xlsx库: npm install xlsx')
      return
    }

    // 创建工作簿
    const wb = XLSX.utils.book_new()

    // 为每个图层创建一个工作表
    state.tabs.forEach((tab: any) => {
      const data = tab.data || []
      if (data.length === 0) return

      // 获取所有字段名（排除geometry字段）
      const allFields = new Set<string>()
      data.forEach((item: any) => {
        Object.keys(item).forEach((key) => {
          if (key !== 'geometry') {
            allFields.add(key)
          }
        })
      })

      const fields = Array.from(allFields)

      // 创建表头
      const headers = fields.map((field) => {
        // 字段名映射
        const fieldMap: Record<string, string> = {
          OBJECTID: '对象ID',
          管材: '管材',
          管径: '管径',
          备注: '设备类型',
          PIPELENGTH: '管长(m)',
          LENGTH: '长度(m)',
          fid: 'ID'
        }
        return fieldMap[field] || field
      })

      // 创建数据行
      const rows = data.map((item: any) => {
        return fields.map((field) => {
          const value = item[field]
          return value !== undefined && value !== null ? String(value) : ''
        })
      })

      // 添加统计汇总行
      const summaryRows: string[][] = []

      // 按管径统计
      const diameterStats = new Map<string, { count: number; length: number }>()
      data.forEach((item: any) => {
        const diameter = item.管径 || '未知'
        const length = Number(item.PIPELENGTH || item.LENGTH || 0)
        
        if (!diameterStats.has(diameter)) {
          diameterStats.set(diameter, { count: 0, length: 0 })
        }
        
        const stats = diameterStats.get(diameter)!
        stats.count++
        stats.length += length
      })

      // 添加管径统计汇总
      summaryRows.push(['管径统计汇总'])
      summaryRows.push(['管径', '数量(个)', '总长度(m)'])
      diameterStats.forEach((stats, diameter) => {
        summaryRows.push([diameter, stats.count.toString(), stats.length.toFixed(2)])
      })

      // 按管材统计
      const materialStats = new Map<string, { count: number; length: number }>()
      data.forEach((item: any) => {
        const material = item.管材 || '未知'
        const length = Number(item.PIPELENGTH || item.LENGTH || 0)
        
        if (!materialStats.has(material)) {
          materialStats.set(material, { count: 0, length: 0 })
        }
        
        const stats = materialStats.get(material)!
        stats.count++
        stats.length += length
      })

      // 添加管材统计汇总
      summaryRows.push([''])
      summaryRows.push(['管材统计汇总'])
      summaryRows.push(['管材', '数量(个)', '总长度(m)'])
      materialStats.forEach((stats, material) => {
        summaryRows.push([material, stats.count.toString(), stats.length.toFixed(2)])
      })

      // 计算总计
      const totalCount = data.length
      const totalLength = data.reduce((sum, item) => {
        return sum + Number(item.PIPELENGTH || item.LENGTH || 0)
      }, 0)

      // 创建详细数据工作表
      const detailSheetData = [
        ['按口径统计管长报告'],
        [''],
        ['统计时间', new Date().toLocaleString()],
        ['查询范围', staticState.graphics ? '自定义区域' : '全图'],
        ['记录总数', totalCount],
        ['总长度', totalLength.toFixed(2) + 'm'],
        [''],
        ['详细数据'],
        headers,
        ...rows,
        [''], // 空行
        ...summaryRows
      ]

      const ws = XLSX.utils.aoa_to_sheet(detailSheetData)
      XLSX.utils.book_append_sheet(wb, ws, `${tab.label || tab.name}-详细数据`)

      // 创建统计图表工作表
      const chartSheetData = createChartData(tab, data)
      if (chartSheetData.length > 0) {
        const chartWs = XLSX.utils.aoa_to_sheet(chartSheetData)
        XLSX.utils.book_append_sheet(wb, chartWs, `${tab.label || tab.name}-统计图表`)
      }
    })

    // 生成文件名
    const now = new Date()
    const timestamp = now
      .toISOString()
      .slice(0, 19)
      .replace(/[:-]/g, '')
      .replace('T', '_')
    const filename = `按口径统计管长_${timestamp}.xlsx`

    // 导出Excel文件
    XLSX.writeFile(wb, filename)

    SLMessage.success('统计结果导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    SLMessage.error('导出失败，请重试')
  }
}

// 创建图表数据
const createChartData = (tab: any, data: any[]) => {
  const chartData: string[][] = []
  
  // 按管径统计
  const diameterStats = new Map<string, { count: number; length: number }>()
  data.forEach((item: any) => {
    const diameter = item.管径 || '未知'
    const length = Number(item.PIPELENGTH || item.LENGTH || 0)
    
    if (!diameterStats.has(diameter)) {
      diameterStats.set(diameter, { count: 0, length: 0 })
    }
    
    const stats = diameterStats.get(diameter)!
    stats.count++
    stats.length += length
  })

  // 管径统计图表数据
  chartData.push(['管径统计图表'])
  chartData.push(['管径', '数量(个)', '总长度(m)', '占比(%)'])
  
  const totalLength = data.reduce((sum, item) => {
    return sum + Number(item.PIPELENGTH || item.LENGTH || 0)
  }, 0)

  diameterStats.forEach((stats, diameter) => {
    const percentage = totalLength > 0 ? ((stats.length / totalLength) * 100).toFixed(2) : '0.00'
    chartData.push([diameter, stats.count.toString(), stats.length.toFixed(2), percentage])
  })

  // 按管材统计
  const materialStats = new Map<string, { count: number; length: number }>()
  data.forEach((item: any) => {
    const material = item.管材 || '未知'
    const length = Number(item.PIPELENGTH || item.LENGTH || 0)
    
    if (!materialStats.has(material)) {
      materialStats.set(material, { count: 0, length: 0 })
    }
    
    const stats = materialStats.get(material)!
    stats.count++
    stats.length += length
  })

  // 管材统计图表数据
  chartData.push([''])
  chartData.push(['管材统计图表'])
  chartData.push(['管材', '数量(个)', '总长度(m)', '占比(%)'])
  
  materialStats.forEach((stats, material) => {
    const percentage = totalLength > 0 ? ((stats.length / totalLength) * 100).toFixed(2) : '0.00'
    chartData.push([material, stats.count.toString(), stats.length.toFixed(2), percentage])
  })

  return chartData
}
const { initSketch, destroySketch } = useSketch()
const resolveDrawEnd = (result: ISketchHandlerParameter) => {
  staticState.graphics = result.graphics[0]
}
const onMapLoaded = view => {
  staticState.view = view
  staticState.graphicsLayer = getGraphicLayer(staticState.view, {
    id: 'search-pipe-diameter',
    title: '按口径统计管长'
  })
  staticState.sketch = initSketch(staticState.view, staticState.graphicsLayer, {
    createCallBack: resolveDrawEnd,
    updateCallBack: resolveDrawEnd
  })
  getLayerInfo()
}
onBeforeUnmount(() => {
  destroySketch()
  staticState.graphicsLayer?.removeAll()
  staticState.graphicsLayer?.destroy()
})
</script>
<style lang="scss" scoped></style>
