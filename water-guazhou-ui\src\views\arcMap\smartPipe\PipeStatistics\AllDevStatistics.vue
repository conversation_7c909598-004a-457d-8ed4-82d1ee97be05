<!-- 全设备统计 -->
<template>
  <RightDrawerMap
    ref="refMap"
    :title="'全设备汇总'"
    :full-content="true"
    @map-loaded="onMapLoaded"
  >
    <Form ref="refForm" :config="FormConfig"></Form>
    <template #detail-header>
      <span>统计结果</span>
    </template>
    <template #detail-default>
      <StatisticsCharts
        ref="refStatistic"
        :tabs="state.tabs"
        :view="staticState.view"
        :all-device="true"
        :query-params="staticState.queryParams"
        :statistics-params="{
          group_fields: ['备注'],
          statistic_field: EStatisticField.OBJECTID,
          statistic_type: '1'
        }"
        unit="个"
        prefix=""
        @detail-refreshed="state.loading = false"
        @attr-row-click="
          (row) => {
            refStatistic?.setCurLayer(row.label);
            refStatistic?.refreshDetail(row.label, true);
          }
        "
        @bar-click="handleChartClick"
        @ring-click="handleChartClick"
      />
    </template>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import { queryLayerClassName } from '@/api/mapservice';
import { useSketch } from '@/hooks/arcgis';
import {
  EStatisticField,
  extentTo,
  getGraphicLayer,
  getLayerOids,
  getSubLayerIds
} from '@/utils/MapHelper';
import { SLMessage } from '@/utils/Message';
// @ts-ignore
import RightDrawerMap from '../../components/common/RightDrawerMap.vue';
// @ts-ignore
import StatisticsCharts from '../../components/components/StatisticsCharts.vue';
import { QueryByPolygon } from '@/utils/geoserver/wfsUtils';

const refStatistic = ref<InstanceType<typeof StatisticsCharts>>();
const refMap = ref<InstanceType<typeof RightDrawerMap>>();
const refForm = ref<IFormIns>();

const state = reactive<{
  tabs: any[];
  loading: boolean;
  layerInfos: any[];
  layerIds: any[];
  attributes: any[];
  curType: 'ellipse' | 'rectangle' | 'polygon' | '';
}>({
  tabs: [],
  curType: '',
  layerInfos: [],
  layerIds: [],
  attributes: [],
  loading: false
});
const staticState: {
  view?: __esri.MapView;
  graphicsLayer?: __esri.GraphicsLayer;
  sketch?: __esri.SketchViewModel;
  queryParams: {
    geometry?: __esri.Geometry;
    where?: string;
  };
} = {
  queryParams: {
    geometry: undefined,
    where: '1=1'
  }
};
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fieldset: {
        desc: '绘制工具'
      },
      fields: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '绘制多边形',
              disabled: () => state.loading,
              iconifyIcon: 'mdi:shape-polygon-plus',
              click: () => initDraw('polygon'),
              styles: {
                flex: '1',
                minWidth: '0'
              }
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '绘制矩形',
              disabled: () => state.loading,
              iconifyIcon: 'ep:crop',
              click: () => initDraw('rectangle'),
              styles: {
                flex: '1',
                minWidth: '0'
              }
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '绘制圆形',
              disabled: () => state.loading,
              iconifyIcon: 'mdi:ellipse-outline',
              click: () => initDraw('circle'),
              styles: {
                flex: '1',
                minWidth: '0'
              }
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '清除图形',
              disabled: () => state.loading,
              iconifyIcon: 'ep:delete',
              click: () => clearGraphicsLayer(),
              styles: {
                flex: '1',
                minWidth: '0'
              }
            }
          ]
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '统计',
              styles: {
                width: '48%'
              },
              loading: () => state.loading,
              click: () => startQuery()
            },
            {
              perm: true,
              text: '导出',
              type: 'primary',
              styles: {
                width: '48%'
              },
              disabled: () => state.loading || state.tabs.length === 0,
              iconifyIcon: 'ep:download',
              click: () => exportStatistics()
            }
          ]
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12
});

const handleChartClick = (params: any) => {
  const tab = params.name;
  refStatistic.value?.setCurLayer(tab);
};

// 导出统计结果
const exportStatistics = async () => {
  try {
    if (state.tabs.length === 0) {
      SLMessage.warning('暂无统计数据可导出');
      return;
    }

    // 动态导入xlsx库
    let XLSX: any;
    try {
      XLSX = await import('xlsx');
    } catch (error) {
      SLMessage.error('请先安装xlsx库: npm install xlsx');
      return;
    }

    // 创建工作簿
    const wb = XLSX.utils.book_new();

    // 为每个图层创建一个工作表
    state.tabs.forEach((tab: any) => {
      const data = tab.data || [];
      if (data.length === 0) return;

      // 获取所有字段名（排除geometry字段）
      const allFields = new Set<string>();
      data.forEach((item: any) => {
        Object.keys(item).forEach((key) => {
          if (key !== 'geometry') {
            allFields.add(key);
          }
        });
      });

      const fields = Array.from(allFields);

      // 创建表头
      const headers = fields.map((field) => {
        // 字段名映射
        const fieldMap: Record<string, string> = {
          OBJECTID: '对象ID',
          管材: '管材',
          管径: '管径',
          备注: '设备类型',
          fid: 'ID'
        };
        return fieldMap[field] || field;
      });

      // 创建数据行
      const rows = data.map((item: any) => {
        return fields.map((field) => {
          const value = item[field];
          return value !== undefined && value !== null ? String(value) : '';
        });
      });

      // 添加统计汇总行
      const summaryRows: string[][] = [];

      // 根据不同图层类型进行统计
      if (tab.name === '管线') {
        // 按管材统计
        const materialStats: Record<string, number> = {};
        const diameterStats: Record<string, number> = {};

        data.forEach((item: any) => {
          const material = item['管材'] || '未知';
          const diameter = item['管径'] || '未知';

          materialStats[material] = (materialStats[material] || 0) + 1;
          diameterStats[diameter] = (diameterStats[diameter] || 0) + 1;
        });

        summaryRows.push(['', '--- 管材统计 ---', '', '', '']);
        Object.entries(materialStats).forEach(([key, value]) => {
          summaryRows.push(['', key, `${value}个`, '', '']);
        });

        summaryRows.push(['', '--- 管径统计 ---', '', '', '']);
        Object.entries(diameterStats).forEach(([key, value]) => {
          summaryRows.push(['', key, `${value}个`, '', '']);
        });
      } else if (tab.name === '测点') {
        // 按设备类型统计
        const deviceStats: Record<string, number> = {};

        data.forEach((item: any) => {
          const deviceType = item['备注'] || '未知';
          deviceStats[deviceType] = (deviceStats[deviceType] || 0) + 1;
        });

        summaryRows.push(['', '--- 设备类型统计 ---', '', '', '']);
        Object.entries(deviceStats).forEach(([key, value]) => {
          summaryRows.push(['', key, `${value}个`, '', '']);
        });
      } else if (tab.name === '测流井') {
        // 按管径统计
        const diameterStats: Record<string, number> = {};

        data.forEach((item: any) => {
          const diameter = item['管径'] || '未知';
          diameterStats[diameter] = (diameterStats[diameter] || 0) + 1;
        });

        summaryRows.push(['', '--- 管径统计 ---', '', '', '']);
        Object.entries(diameterStats).forEach(([key, value]) => {
          summaryRows.push(['', key, `${value}个`, '', '']);
        });
      }

      // 创建详细数据工作表
      const detailSheetData = [
        headers,
        ...rows,
        [''], // 空行
        [`总计: ${data.length}个`],
        [''], // 空行
        ...summaryRows
      ];

      const ws = XLSX.utils.aoa_to_sheet(detailSheetData);
      XLSX.utils.book_append_sheet(wb, ws, `${tab.label}-详细数据`);

      // 创建统计图表工作表
      const chartSheetData = createChartData(tab, data);
      if (chartSheetData.length > 0) {
        const chartWs = XLSX.utils.aoa_to_sheet(chartSheetData);
        XLSX.utils.book_append_sheet(wb, chartWs, `${tab.label}-统计图表`);
      }
    });

    // 生成文件名
    const now = new Date();
    const timestamp = now
      .toISOString()
      .slice(0, 19)
      .replace(/[:-]/g, '')
      .replace('T', '_');
    const filename = `全设备统计结果_${timestamp}.xlsx`;

    // 导出Excel文件
    XLSX.writeFile(wb, filename);

    SLMessage.success('统计结果导出成功');
  } catch (error) {
    console.error('导出失败:', error);
    SLMessage.error('导出失败，请重试');
  }
};

// 创建图表数据
const createChartData = (tab: any, data: any[]) => {
  const chartData: string[][] = [];

  if (tab.name === '管线') {
    // 管材统计图表
    const materialStats: Record<string, number> = {};
    const diameterStats: Record<string, number> = {};

    data.forEach((item: any) => {
      const material = item['管材'] || '未知';
      const diameter = item['管径'] || '未知';

      materialStats[material] = (materialStats[material] || 0) + 1;
      diameterStats[diameter] = (diameterStats[diameter] || 0) + 1;
    });

    chartData.push(['管材统计直方图']);
    chartData.push(['管材类型', '数量']);
    Object.entries(materialStats).forEach(([key, value]) => {
      chartData.push([key, value.toString()]);
    });

    chartData.push(['']); // 空行分隔

    chartData.push(['管径统计直方图']);
    chartData.push(['管径规格', '数量']);
    Object.entries(diameterStats).forEach(([key, value]) => {
      chartData.push([key, value.toString()]);
    });
  } else if (tab.name === '测点') {
    // 设备类型统计图表
    const deviceStats: Record<string, number> = {};

    data.forEach((item: any) => {
      const deviceType = item['备注'] || '未知';
      deviceStats[deviceType] = (deviceStats[deviceType] || 0) + 1;
    });

    chartData.push(['设备类型统计直方图']);
    chartData.push(['设备类型', '数量']);
    Object.entries(deviceStats).forEach(([key, value]) => {
      chartData.push([key, value.toString()]);
    });
  } else if (tab.name === '测流井') {
    // 管径统计图表
    const diameterStats: Record<string, number> = {};

    data.forEach((item: any) => {
      const diameter = item['管径'] || '未知';
      diameterStats[diameter] = (diameterStats[diameter] || 0) + 1;
    });

    chartData.push(['设备井管径统计直方图']);
    chartData.push(['管径规格', '数量']);
    Object.entries(diameterStats).forEach(([key, value]) => {
      chartData.push([key, value.toString()]);
    });
  }

  return chartData;
};

const initDraw = (type: any) => {
  clearGraphicsLayer();
  staticState.sketch?.create(type);
};
const clearGraphicsLayer = () => {
  staticState.graphicsLayer?.removeAll();
  staticState.queryParams.geometry = undefined;
};

const getLayerInfo = async () => {
  // setTimeout(()=>{
  //   const layerInfo = staticState.view?.layerViews.items[0].layer.sublayers;
  //   // state.layerIds = getSubLayerIds(staticState.view)
  //   // const layerInfo = await queryLayerClassName(state.layerIds)
  //   state.layerInfos = layerInfo.items || []
  // },2000)
};

const startQuery = async () => {
  SLMessage.info('正在统计，请稍候...');
  try {
    state.loading = true;
    state.tabs.length = 0;

    const where = staticState.queryParams.where || '1=1';
    // 支持空间圈选
    let polygonCoordinates = undefined;
    if (
      staticState.queryParams.geometry &&
      staticState.queryParams.geometry.type === 'polygon'
    ) {
      const rings = (staticState.queryParams.geometry as any).rings;
      if (Array.isArray(rings) && rings.length > 0) {
        polygonCoordinates = rings[0];
      }
    }

    // 定义图层配置 - 根据您的需求：管线统计材质和管径，测点统计设备类型
    const layerConfigs = [
      {
        name: '管线',
        label: '管线',
        statisticsFields: ['管材', '管径']
      },
      {
        name: '测点',
        label: '测点',
        statisticsFields: ['备注'],
        filterValues: ['三通', '四通', '消防', '排气', '水表']
      },
      {
        name: '测流井',
        label: '测流井',
        statisticsFields: ['管径']
        // filterValues: ['三通', '四通', '消防', '排气', '水表']
      }
    ];

    // 为每个图层创建一个Tab，合并该图层的所有统计字段
    const promises: Promise<any>[] = [];

    layerConfigs.forEach((layerConfig) => {
      const promise = (async () => {
        try {
          console.log(`开始查询${layerConfig.name}图层数据进行前端统计`);

          // 直接查询全部数据，前端统计
          const response = await QueryByPolygon(
            layerConfig.name,
            polygonCoordinates,
            where
          );
          const features = response.data?.features || [];

          console.log(
            `查询到${layerConfig.name}图层数据${features.length}条，开始统计`
          );

          // 为该图层的每个要素创建包含所有统计字段信息的数据项
          const allDataArray: any[] = [];

          features.forEach((feature, index) => {
            const properties = feature.properties || {};

            // 检查是否有任何统计字段符合过滤条件
            let shouldInclude = false;
            if (layerConfig.filterValues) {
              // 如果有过滤值，检查任何一个统计字段是否符合条件
              shouldInclude = layerConfig.statisticsFields.some((field) => {
                const fieldValue = properties[field] || '未知';
                return layerConfig.filterValues!.includes(fieldValue);
              });
            } else {
              // 如果没有过滤值，直接包含
              shouldInclude = true;
            }

            if (shouldInclude) {
              // 每个要素只创建一条数据记录，包含所有统计字段和几何信息
              allDataArray.push({
                OBJECTID: properties.OBJECTID || properties.fid || index + 1,
                // 保留原始要素的所有属性
                ...properties,
                // 保留几何信息供地图高亮使用
                geometry: feature.geometry
              });
            }
          });

          return {
            name: layerConfig.name,
            label: layerConfig.label,
            data: allDataArray
          };
        } catch (error) {
          console.error(`查询和统计${layerConfig.name}失败:`, error);
          return {
            name: layerConfig.name,
            label: layerConfig.label,
            data: []
          };
        }
      })();

      promises.push(promise);
    });

    const results = await Promise.all(promises);
    state.tabs = results.filter((result) => result.data.length > 0); // 只保留有数据的Tab

    console.log('统计结果:', results);
    console.log('最终state.tabs:', state.tabs);

    refMap.value?.toggleCustomDetail(true);

    nextTick(() => {
      refStatistic.value?.refreshChar();
    });

    extentTo(
      staticState.view,
      staticState.queryParams.geometry?.extent || staticState.view?.extent,
      true
    );
  } catch (error) {
    console.log(error);
    state.loading = false;
    SLMessage.error('统计失败');
  }
  state.loading = false;
};
const { initSketch, destroySketch } = useSketch();
const resolveDrawEnd = (result: ISketchHandlerParameter) => {
  staticState.queryParams.geometry = result.graphics[0]?.geometry;
};
const onMapLoaded = (view: __esri.MapView) => {
  staticState.view = view;
  staticState.graphicsLayer = getGraphicLayer(staticState.view, {
    id: 'search-alldevice',
    title: '全设备统计'
  });
  staticState.sketch = initSketch(staticState.view, staticState.graphicsLayer, {
    createCallBack: resolveDrawEnd,
    updateCallBack: resolveDrawEnd
  });
  getLayerInfo();
};
onBeforeUnmount(() => {
  destroySketch();
  staticState.sketch = undefined;
});
</script>
<style lang="scss" scoped></style>
