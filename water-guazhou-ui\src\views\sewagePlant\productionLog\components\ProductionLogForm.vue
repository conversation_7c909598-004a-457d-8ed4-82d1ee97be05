<!-- 生产日志表单 -->
<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑生产日志' : '新增生产日志'"
    width="80%"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      class="production-log-form"
    >
      <el-row :gutter="20">
        <!-- 基本信息 -->
        <el-col :span="12">
          <el-form-item label="站点选择" prop="stationId">
            <el-select
              v-model="formData.stationId"
              placeholder="请选择站点"
              style="width: 100%"
              @change="handleStationChange"
            >
              <el-option
                v-for="station in stationList"
                :key="station.stationId"
                :label="station.name"
                :value="station.stationId"
              />
            </el-select>
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="日志日期" prop="logDate">
            <el-date-picker
              v-model="formData.logDate"
              type="date"
              placeholder="请选择日期"
              style="width: 100%"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="天气" prop="weather">
            <el-select v-model="formData.weather" placeholder="请选择天气" style="width: 100%">
              <el-option label="晴" value="晴" />
              <el-option label="阴" value="阴" />
              <el-option label="雨" value="雨" />
              <el-option label="雪" value="雪" />
              <el-option label="雾" value="雾" />
            </el-select>
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="操作员" prop="operatorName">
            <el-input v-model="formData.operatorName" placeholder="请输入操作员姓名" />
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 进水指标 -->
      <el-divider content-position="left">进水指标</el-divider>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="COD (mg/L)" prop="codInlet">
            <el-input-number
              v-model="formData.codInlet"
              :precision="1"
              :min="0"
              style="width: 100%"
              placeholder="请输入COD值"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="8">
          <el-form-item label="BOD5 (mg/L)" prop="bodInlet">
            <el-input-number
              v-model="formData.bodInlet"
              :precision="1"
              :min="0"
              style="width: 100%"
              placeholder="请输入BOD5值"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="8">
          <el-form-item label="悬浮物 (mg/L)" prop="suspendedSolidsInlet">
            <el-input-number
              v-model="formData.suspendedSolidsInlet"
              :precision="1"
              :min="0"
              style="width: 100%"
              placeholder="请输入悬浮物值"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="8">
          <el-form-item label="氨氮 (mg/L)" prop="ammoniaNitrogenInlet">
            <el-input-number
              v-model="formData.ammoniaNitrogenInlet"
              :precision="1"
              :min="0"
              style="width: 100%"
              placeholder="请输入氨氮值"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="8">
          <el-form-item label="总氮 (mg/L)" prop="totalNitrogenInlet">
            <el-input-number
              v-model="formData.totalNitrogenInlet"
              :precision="1"
              :min="0"
              style="width: 100%"
              placeholder="请输入总氮值"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="8">
          <el-form-item label="总磷 (mg/L)" prop="totalPhosphorusInlet">
            <el-input-number
              v-model="formData.totalPhosphorusInlet"
              :precision="1"
              :min="0"
              style="width: 100%"
              placeholder="请输入总磷值"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="8">
          <el-form-item label="PH值" prop="phInlet">
            <el-input-number
              v-model="formData.phInlet"
              :precision="1"
              :min="0"
              :max="14"
              style="width: 100%"
              placeholder="请输入PH值"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 出水指标 -->
      <el-divider content-position="left">出水指标</el-divider>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="COD (mg/L)" prop="codOutlet">
            <el-input-number
              v-model="formData.codOutlet"
              :precision="1"
              :min="0"
              style="width: 100%"
              placeholder="请输入COD值"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="8">
          <el-form-item label="BOD5 (mg/L)" prop="bodOutlet">
            <el-input-number
              v-model="formData.bodOutlet"
              :precision="1"
              :min="0"
              style="width: 100%"
              placeholder="请输入BOD5值"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="8">
          <el-form-item label="悬浮物 (mg/L)" prop="suspendedSolidsOutlet">
            <el-input-number
              v-model="formData.suspendedSolidsOutlet"
              :precision="1"
              :min="0"
              style="width: 100%"
              placeholder="请输入悬浮物值"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="8">
          <el-form-item label="氨氮 (mg/L)" prop="ammoniaNitrogenOutlet">
            <el-input-number
              v-model="formData.ammoniaNitrogenOutlet"
              :precision="1"
              :min="0"
              style="width: 100%"
              placeholder="请输入氨氮值"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="8">
          <el-form-item label="总氮 (mg/L)" prop="totalNitrogenOutlet">
            <el-input-number
              v-model="formData.totalNitrogenOutlet"
              :precision="1"
              :min="0"
              style="width: 100%"
              placeholder="请输入总氮值"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="8">
          <el-form-item label="总磷 (mg/L)" prop="totalPhosphorusOutlet">
            <el-input-number
              v-model="formData.totalPhosphorusOutlet"
              :precision="1"
              :min="0"
              style="width: 100%"
              placeholder="请输入总磷值"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="8">
          <el-form-item label="PH值" prop="phOutlet">
            <el-input-number
              v-model="formData.phOutlet"
              :precision="1"
              :min="0"
              :max="14"
              style="width: 100%"
              placeholder="请输入PH值"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 生产指标 -->
      <el-divider content-position="left">生产指标</el-divider>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="流量 (m³/h)" prop="flowRate">
            <el-input-number
              v-model="formData.flowRate"
              :precision="1"
              :min="0"
              style="width: 100%"
              placeholder="请输入流量"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="8">
          <el-form-item label="耗电量 (kWh)" prop="powerConsumption">
            <el-input-number
              v-model="formData.powerConsumption"
              :precision="1"
              :min="0"
              style="width: 100%"
              placeholder="请输入耗电量"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="8">
          <el-form-item label="药剂消耗 (kg)" prop="chemicalConsumption">
            <el-input-number
              v-model="formData.chemicalConsumption"
              :precision="1"
              :min="0"
              style="width: 100%"
              placeholder="请输入药剂消耗量"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="8">
          <el-form-item label="污泥产量 (t)" prop="sludgeProduction">
            <el-input-number
              v-model="formData.sludgeProduction"
              :precision="1"
              :min="0"
              style="width: 100%"
              placeholder="请输入污泥产量"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 备注 -->
      <el-row>
        <el-col :span="24">
          <el-form-item label="备注" prop="remarks">
            <el-input
              v-model="formData.remarks"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          {{ isEdit ? '更新' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import dayjs from 'dayjs'
import { useBusinessStore } from '@/store'
import { getSewagePlantStationList, createProductionLog, updateProductionLog } from '../api'
import type { ProductionLogData, SewagePlantStation } from '../types'

// Emits
const emit = defineEmits<{
  refresh: []
}>()

// Store
const businessStore = useBusinessStore()

// Refs
const formRef = ref<FormInstance>()

// State
const dialogVisible = ref(false)
const loading = ref(false)
const isEdit = ref(false)
const stationList = ref<SewagePlantStation[]>([])

// 表单数据
const formData = reactive<Partial<ProductionLogData>>({
  stationId: '',
  stationName: '',
  logDate: dayjs().format('YYYY-MM-DD'),
  weather: '晴',
  codInlet: 0,
  bodInlet: 0,
  suspendedSolidsInlet: 0,
  ammoniaNitrogenInlet: 0,
  totalNitrogenInlet: 0,
  totalPhosphorusInlet: 0,
  phInlet: 7,
  codOutlet: 0,
  bodOutlet: 0,
  suspendedSolidsOutlet: 0,
  ammoniaNitrogenOutlet: 0,
  totalNitrogenOutlet: 0,
  totalPhosphorusOutlet: 0,
  phOutlet: 7,
  flowRate: 0,
  powerConsumption: 0,
  chemicalConsumption: 0,
  sludgeProduction: 0,
  operatorName: '',
  remarks: ''
})

// 表单验证规则
const formRules: FormRules = {
  stationId: [
    { required: true, message: '请选择站点', trigger: 'change' }
  ],
  logDate: [
    { required: true, message: '请选择日志日期', trigger: 'change' }
  ],
  weather: [
    { required: true, message: '请选择天气', trigger: 'change' }
  ],
  operatorName: [
    { required: true, message: '请输入操作员姓名', trigger: 'blur' }
  ],
  codInlet: [
    { required: true, message: '请输入进水COD值', trigger: 'blur' }
  ],
  bodInlet: [
    { required: true, message: '请输入进水BOD5值', trigger: 'blur' }
  ],
  codOutlet: [
    { required: true, message: '请输入出水COD值', trigger: 'blur' }
  ],
  bodOutlet: [
    { required: true, message: '请输入出水BOD5值', trigger: 'blur' }
  ],
  flowRate: [
    { required: true, message: '请输入流量', trigger: 'blur' }
  ]
}

// 加载站点列表
const loadStationList = async () => {
  try {
    const response = await getSewagePlantStationList({
      projectId: businessStore.selectedProject?.value
    })

    const dataList = response.data?.data || response.data || []

    if (Array.isArray(dataList) && dataList.length > 0) {
      // 过滤出污水厂
      const sewagePlants = dataList.filter((item: any) => {
        const name = item.name || item.stationName || ''
        return name.includes('污水') || name.includes('污水厂') || name.includes('污水处理厂')
      })

      stationList.value = sewagePlants.map((item: any) => ({
        id: item.stationId || item.id,
        stationId: item.stationId || item.id,
        name: item.name || item.stationName,
        stationName: item.stationName || item.name,
        location: item.location,
        status: item.status,
        projectId: item.projectId,
        projectName: item.projectName
      }))
    }
  } catch (error) {
    console.error('加载站点列表失败:', error)
    ElMessage.error('加载站点列表失败')
  }
}

// 站点变化处理
const handleStationChange = (stationId: string) => {
  const station = stationList.value.find(s => s.stationId === stationId)
  if (station) {
    formData.stationName = station.name || station.stationName || ''
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    id: undefined,
    stationId: '',
    stationName: '',
    logDate: dayjs().format('YYYY-MM-DD'),
    weather: '晴',
    codInlet: 0,
    bodInlet: 0,
    suspendedSolidsInlet: 0,
    ammoniaNitrogenInlet: 0,
    totalNitrogenInlet: 0,
    totalPhosphorusInlet: 0,
    phInlet: 7,
    codOutlet: 0,
    bodOutlet: 0,
    suspendedSolidsOutlet: 0,
    ammoniaNitrogenOutlet: 0,
    totalNitrogenOutlet: 0,
    totalPhosphorusOutlet: 0,
    phOutlet: 7,
    flowRate: 0,
    powerConsumption: 0,
    chemicalConsumption: 0,
    sludgeProduction: 0,
    operatorName: '',
    remarks: ''
  })
  formRef.value?.clearValidate()
}

// 打开对话框
const open = async (data?: ProductionLogData) => {
  await loadStationList()

  if (data) {
    isEdit.value = true
    Object.assign(formData, data)
  } else {
    isEdit.value = false
    resetForm()
  }

  dialogVisible.value = true
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    if (isEdit.value && formData.id) {
      await updateProductionLog(formData.id, formData)
      ElMessage.success('更新成功')
    } else {
      await createProductionLog(formData as Omit<ProductionLogData, 'id' | 'createTime' | 'updateTime'>)
      ElMessage.success('保存成功')
    }

    emit('refresh')
    handleClose()
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  } finally {
    loading.value = false
  }
}

// 暴露方法
defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.production-log-form {
  max-height: 60vh;
  overflow-y: auto;

  :deep(.el-form-item__label) {
    font-weight: 500;
  }

  :deep(.el-divider__text) {
    font-weight: 600;
    color: var(--el-color-primary);
  }
}

.dialog-footer {
  text-align: right;
}
</style>
