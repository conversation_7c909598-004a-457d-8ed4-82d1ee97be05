<!-- 污水处理厂生产日志 -->
<template>
  <div class="wrapper">
    <div
      v-loading="totalLoading"
      class="main"
    >
      <div class="right">
        <CardSearch
          ref="cardSearch"
          :config="cardSearchConfig"
        />
        <CardTable
          id="print"
          ref="refTable"
          class="card-table"
          :config="cardTableConfig"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs'
import { Printer } from '@element-plus/icons-vue'

import { getFormatTreeNodeDeepestChild, objectLookup } from '@/utils/GlobalHelper'
import { printJSON } from '@/utils/printUtils'
import type { ProductionLogData } from './types'
import useStation from '@/hooks/station/useStation'

const { getStationTree } = useStation()
const cardSearch = ref()
const refTable = ref()

const state = reactive({
  queryType: 'day',
  treeDataType: 'Station',
  stationId: '',
  title: ''
})
const totalLoading = ref<boolean>(false)
const today = dayjs().date()

// 污水处理厂站点树
const TreeData = reactive({
  data: [] as any[],
  currentProject: {} as any
})

// 搜索栏初始化配置
const cardSearchConfig = reactive({
  defaultParams: {
    type: 'day',
    year: [dayjs().format(), dayjs().format()],
    month: [dayjs().format(), dayjs().format()],
    day: [dayjs().date(today - 6).format('YYYY-MM-DD'), dayjs().date(today).format('YYYY-MM-DD')]
  },
  filters: [
    {
      type: 'select-tree',
      field: 'treeData',
      checkStrictly: true,
      defaultExpandAll: true,
      options: computed(() => TreeData.data) as any,
      label: '站点选择',
      onChange: (key: any) => {
        const val = objectLookup(TreeData.data, 'children', 'id', key)
        TreeData.currentProject = val
        state.treeDataType = val.data.type as string
        if (state.treeDataType === 'Station') {
          state.stationId = val.id as string
          refreshData()
        }
      }
    },
    {
      type: 'radio-button',
      field: 'type',
      options: [
        { label: '日报', value: 'day' },
        { label: '月报', value: 'month' },
        { label: '年报', value: 'year' }
      ],
      label: '报告类型'
    },
    {
      type: 'daterange',
      label: '选择时间',
      field: 'day',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: any) => {
        formItem.hidden = params.type === 'month' || params.type === 'year'
      }
    },
    {
      type: 'monthrange',
      label: '选择时间',
      field: 'month',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: any) => {
        formItem.hidden = params.type === 'day' || params.type === 'year'
      }
    },
    {
      type: 'yearrange',
      label: '选择时间',
      field: 'year',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: any) => {
        formItem.hidden = params.type === 'month' || params.type === 'day'
      }
    },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          click: () => refreshData(),
          icon: 'iconfont icon-chaxun'
        },
        {
          text: '导出',
          perm: true,
          type: 'warning',
          icon: 'iconfont icon-xiazai',
          click: () => _exportProductionLog()
        },
        {
          perm: true,
          text: '打印',
          type: 'success',
          svgIcon: shallowRef(Printer),
          click: () => handlePrint()
        }
      ]
    }
  ]
})

// 表格配置
const cardTableConfig = reactive({
  title: '污水厂生产日志',
  loading: false,
  dataList: [] as ProductionLogData[],
  pagination: {
    hide: true
  },
  columns: [
    { prop: 'logDate', label: '采样时间', minWidth: 120 },
    { prop: 'stationName', label: '采样点位', minWidth: 150 },
    { prop: 'weather', label: '水质类别', minWidth: 100 },
    {
      prop: 'codOutlet',
      label: 'COD mg/L',
      minWidth: 120,
      formatter: (row: ProductionLogData) => row.codOutlet
    },
    {
      prop: 'ammoniaNitrogenOutlet',
      label: '氨氮 mg/L',
      minWidth: 120,
      formatter: (row: ProductionLogData) => row.ammoniaNitrogenOutlet
    },
    {
      prop: 'totalNitrogenOutlet',
      label: '总氮 mg/L',
      minWidth: 120,
      formatter: (row: ProductionLogData) => row.totalNitrogenOutlet
    },
    {
      prop: 'totalPhosphorusOutlet',
      label: '总磷 mg/L',
      minWidth: 120,
      formatter: (row: ProductionLogData) => row.totalPhosphorusOutlet
    },
    {
      prop: 'phOutlet',
      label: '出水PH值',
      minWidth: 120,
      formatter: (row: ProductionLogData) => row.phOutlet
    },
    {
      prop: 'flowRate',
      label: '流量 m³/h',
      minWidth: 120,
      formatter: (row: ProductionLogData) => row.flowRate
    }
  ],
  operations: [
    {
      label: '打印',
      icon: shallowRef(Printer),
      click: () => printJSON({
        title: '污水厂生产日志',
        data: cardTableConfig.dataList,
        titleList: cardTableConfig.columns
      })
    }
  ]
})

// 刷新数据
const refreshData = () => {
  cardTableConfig.loading = true

  // 模拟数据 - 使用当前时间查询条件范围内的时间
  const today = dayjs()

  const mockData: ProductionLogData[] = [
    {
      id: '1',
      stationId: 'ST001',
      stationName: '污水处理厂A',
      logDate: today.format('YYYY-MM-DD HH:mm:ss'),
      weather: 'Ⅰ类',
      codOutlet: 15.2,
      ammoniaNitrogenOutlet: 0.8,
      totalNitrogenOutlet: 8.5,
      totalPhosphorusOutlet: 0.3,
      phOutlet: 7.2,
      flowRate: 1200
    },
    {
      id: '2',
      stationId: 'ST002',
      stationName: '污水处理厂B',
      logDate: today.subtract(1, 'day').format('YYYY-MM-DD HH:mm:ss'),
      weather: 'Ⅱ类',
      codOutlet: 18.5,
      ammoniaNitrogenOutlet: 1.2,
      totalNitrogenOutlet: 9.8,
      totalPhosphorusOutlet: 0.4,
      phOutlet: 7.5,
      flowRate: 980
    },
    {
      id: '3',
      stationId: 'ST003',
      stationName: '污水处理厂C',
      logDate: today.subtract(2, 'day').format('YYYY-MM-DD HH:mm:ss'),
      weather: 'Ⅰ类',
      codOutlet: 12.8,
      ammoniaNitrogenOutlet: 0.6,
      totalNitrogenOutlet: 7.2,
      totalPhosphorusOutlet: 0.2,
      phOutlet: 6.9,
      flowRate: 1450
    },
    {
      id: '4',
      stationId: 'ST004',
      stationName: '污水处理厂D',
      logDate: today.subtract(3, 'day').format('YYYY-MM-DD HH:mm:ss'),
      weather: 'Ⅲ类',
      codOutlet: 22.1,
      ammoniaNitrogenOutlet: 1.8,
      totalNitrogenOutlet: 12.3,
      totalPhosphorusOutlet: 0.6,
      phOutlet: 7.8,
      flowRate: 750
    },
    {
      id: '5',
      stationId: 'ST005',
      stationName: '污水处理厂E',
      logDate: today.subtract(4, 'day').format('YYYY-MM-DD HH:mm:ss'),
      weather: 'Ⅱ类',
      codOutlet: 16.7,
      ammoniaNitrogenOutlet: 1.0,
      totalNitrogenOutlet: 8.9,
      totalPhosphorusOutlet: 0.35,
      phOutlet: 7.3,
      flowRate: 1100
    }
  ]

  // 模拟异步加载
  setTimeout(() => {
    cardTableConfig.dataList = mockData
    cardTableConfig.loading = false
  }, 500)
}

// 导出生产日志
const _exportProductionLog = () => {
  console.log('导出生产日志功能')
  // 这里可以实现具体的导出逻辑
}

// 打印报表
const handlePrint = () => {
  printJSON({ title: state.title, titleList: cardTableConfig.columns, data: cardTableConfig.dataList })
}



onMounted(async () => {
  const treeData = await getStationTree('污水处理厂')
  TreeData.data = treeData
  TreeData.currentProject = getFormatTreeNodeDeepestChild(TreeData.data)
  cardSearchConfig.defaultParams = { ...cardSearchConfig.defaultParams, treeData: TreeData.currentProject } as any
  cardSearch.value?.resetForm()

  // 直接加载模拟数据 - 使用当前时间查询条件范围内的时间
  const today = dayjs()
  const startDate = today.subtract(6, 'day')

  const mockData: ProductionLogData[] = [
    {
      id: '1',
      stationId: 'ST001',
      stationName: '污水处理厂A',
      logDate: today.format('YYYY-MM-DD HH:mm:ss'),
      weather: 'Ⅰ类',
      codOutlet: 15.2,
      ammoniaNitrogenOutlet: 0.8,
      totalNitrogenOutlet: 8.5,
      totalPhosphorusOutlet: 0.3,
      phOutlet: 7.2,
      flowRate: 1200
    },
    {
      id: '2',
      stationId: 'ST002',
      stationName: '污水处理厂B',
      logDate: today.subtract(1, 'day').format('YYYY-MM-DD HH:mm:ss'),
      weather: 'Ⅱ类',
      codOutlet: 18.5,
      ammoniaNitrogenOutlet: 1.2,
      totalNitrogenOutlet: 9.8,
      totalPhosphorusOutlet: 0.4,
      phOutlet: 7.5,
      flowRate: 980
    },
    {
      id: '3',
      stationId: 'ST003',
      stationName: '污水处理厂C',
      logDate: today.subtract(2, 'day').format('YYYY-MM-DD HH:mm:ss'),
      weather: 'Ⅰ类',
      codOutlet: 12.8,
      ammoniaNitrogenOutlet: 0.6,
      totalNitrogenOutlet: 7.2,
      totalPhosphorusOutlet: 0.2,
      phOutlet: 6.9,
      flowRate: 1450
    },
    {
      id: '4',
      stationId: 'ST004',
      stationName: '污水处理厂D',
      logDate: today.subtract(3, 'day').format('YYYY-MM-DD HH:mm:ss'),
      weather: 'Ⅲ类',
      codOutlet: 22.1,
      ammoniaNitrogenOutlet: 1.8,
      totalNitrogenOutlet: 12.3,
      totalPhosphorusOutlet: 0.6,
      phOutlet: 7.8,
      flowRate: 750
    },
    {
      id: '5',
      stationId: 'ST005',
      stationName: '污水处理厂E',
      logDate: today.subtract(4, 'day').format('YYYY-MM-DD HH:mm:ss'),
      weather: 'Ⅱ类',
      codOutlet: 16.7,
      ammoniaNitrogenOutlet: 1.0,
      totalNitrogenOutlet: 8.9,
      totalPhosphorusOutlet: 0.35,
      phOutlet: 7.3,
      flowRate: 1100
    }
  ]

  cardTableConfig.dataList = mockData
})
</script>

<style lang="scss" scoped>
.wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
}

.main {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.right {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.card-table {
  flex: 1;
}
</style>