<!-- 污水处理厂生产日志 -->
<template>
  <div class="wrapper">
    <div
      v-loading="totalLoading"
      class="main"
    >
      <div class="right">
        <CardSearch
          ref="cardSearch"
          :config="cardSearchConfig"
        />
        <CardTable
          id="print"
          ref="refTable"
          class="card-table"
          :config="cardTableConfig"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs'
import { Printer } from '@element-plus/icons-vue'

import { getFormatTreeNodeDeepestChild, objectLookup } from '@/utils/GlobalHelper'
import { printJSON } from '@/utils/printUtils'
import { getProductionLogList, getSewagePlantStationList } from './api'
import type { ProductionLogData, QueryForm } from './types'
import { useBusinessStore } from '@/store'

const businessStore = useBusinessStore()
const cardSearch = ref()
const refTable = ref()

const state = reactive({
  queryType: 'day',
  treeDataType: 'Station',
  stationId: '',
  title: ''
})
const totalLoading = ref<boolean>(false)
const today = dayjs().date()

// 污水处理厂站点树
const TreeData = reactive({
  data: [] as any[],
  currentProject: {} as any
})

// 搜索栏初始化配置
const cardSearchConfig = reactive({
  defaultParams: {
    type: 'day',
    year: [dayjs().format(), dayjs().format()],
    month: [dayjs().format(), dayjs().format()],
    day: [dayjs().date(today - 6).format('YYYY-MM-DD'), dayjs().date(today).format('YYYY-MM-DD')]
  },
  filters: [
    {
      type: 'select-tree',
      field: 'treeData',
      checkStrictly: true,
      defaultExpandAll: true,
      options: computed(() => TreeData.data) as any,
      label: '站点选择',
      onChange: (key: any) => {
        const val = objectLookup(TreeData.data, 'children', 'id', key)
        TreeData.currentProject = val
        state.treeDataType = val.data.type as string
        if (state.treeDataType === 'Station') {
          state.stationId = val.id as string
          refreshData()
        }
      }
    },
    {
      type: 'radio-button',
      field: 'type',
      options: [
        { label: '日报', value: 'day' },
        { label: '月报', value: 'month' },
        { label: '年报', value: 'year' }
      ],
      label: '报告类型'
    },
    {
      type: 'daterange',
      label: '选择时间',
      field: 'day',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: any) => {
        formItem.hidden = params.type === 'month' || params.type === 'year'
      }
    },
    {
      type: 'monthrange',
      label: '选择时间',
      field: 'month',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: any) => {
        formItem.hidden = params.type === 'day' || params.type === 'year'
      }
    },
    {
      type: 'yearrange',
      label: '选择时间',
      field: 'year',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: any) => {
        formItem.hidden = params.type === 'month' || params.type === 'day'
      }
    },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          click: () => refreshData(),
          icon: 'iconfont icon-chaxun'
        },
        {
          text: '导出',
          perm: true,
          type: 'warning',
          icon: 'iconfont icon-xiazai',
          click: () => _exportProductionLog()
        },
        {
          perm: true,
          text: '打印',
          type: 'success',
          svgIcon: shallowRef(Printer),
          click: () => handlePrint()
        }
      ]
    }
  ]
})

// 表格配置
const cardTableConfig = reactive({
  title: '污水厂生产日志',
  data: [],
  loading: false,
  pagination: {
    page: 1,
    limit: 20,
    total: 0
  },
  columns: [
    { prop: 'logDate', label: '日期', width: 120, sortable: true },
    { prop: 'stationName', label: '站点名称', width: 150 },
    { prop: 'weather', label: '天气', width: 80 },
    { prop: 'operatorName', label: '操作员', width: 100 },
    {
      prop: 'codInlet',
      label: '进水COD(mg/L)',
      width: 120,
      formatter: (row: ProductionLogData) => row.codInlet
    },
    {
      prop: 'codOutlet',
      label: '出水COD(mg/L)',
      width: 120,
      formatter: (row: ProductionLogData) => row.codOutlet
    },
    {
      prop: 'bodInlet',
      label: '进水BOD5(mg/L)',
      width: 130,
      formatter: (row: ProductionLogData) => row.bodInlet
    },
    {
      prop: 'bodOutlet',
      label: '出水BOD5(mg/L)',
      width: 130,
      formatter: (row: ProductionLogData) => row.bodOutlet
    },
    {
      prop: 'ammoniaNitrogenInlet',
      label: '进水氨氮(mg/L)',
      width: 130,
      formatter: (row: ProductionLogData) => row.ammoniaNitrogenInlet
    },
    {
      prop: 'ammoniaNitrogenOutlet',
      label: '出水氨氮(mg/L)',
      width: 130,
      formatter: (row: ProductionLogData) => row.ammoniaNitrogenOutlet
    },
    {
      prop: 'flowRate',
      label: '流量(m³/h)',
      width: 120,
      formatter: (row: ProductionLogData) => row.flowRate
    },
    { prop: 'remarks', label: '备注', minWidth: 150 }
  ],
  operations: [
    {
      label: '打印',
      icon: shallowRef(Printer),
      click: () => printJSON({
        title: '污水厂生产日志',
        data: cardTableConfig.data,
        titleList: cardTableConfig.columns
      })
    }
  ],
  request: async (params: any) => {
    const queryParams = cardSearch.value?.queryParams as QueryForm || {}
    const requestParams: QueryForm = {
      ...queryParams,
      pageNum: params.page || 1,
      pageSize: params.limit || 20
    }

    const response = await getProductionLogList(requestParams)

    if (response.code === 200 && response.data) {
      return {
        list: response.data.list || [],
        total: response.data.total || 0
      }
    }
    return { list: [], total: 0 }
  }
})

// 刷新数据
const refreshData = () => {
  refTable.value?.refresh()
}

// 导出生产日志
const _exportProductionLog = () => {
  refTable.value?.exportTable()
}

// 打印报表
const handlePrint = () => {
  printJSON({ title: state.title, titleList: cardTableConfig.columns, data: cardTableConfig.dataList })
}

// 加载站点列表
const loadStationList = async () => {
  try {
    const response = await getSewagePlantStationList({
      projectId: businessStore.selectedProject?.value
    })

    const dataList = response.data?.data || response.data || []

    if (Array.isArray(dataList) && dataList.length > 0) {
      const sewagePlants = dataList.filter((item: any) => {
        const name = item.name || item.stationName || ''
        return name.includes('污水') || name.includes('污水厂') || name.includes('污水处理厂')
      })

      TreeData.data = sewagePlants.map((item: any) => ({
        id: item.stationId || item.id,
        label: item.name || item.stationName,
        data: { type: 'Station' }
      }))
    }
  } catch (error) {
    console.error('加载站点列表失败:', error)
  }
}

onMounted(async () => {
  await loadStationList()
})
</script>

<style lang="scss" scoped>
.wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
}

.main {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.right {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.card-table {
  flex: 1;
}
</style>