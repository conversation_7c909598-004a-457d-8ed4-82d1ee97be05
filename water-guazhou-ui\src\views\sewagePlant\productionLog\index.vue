<!-- 污水处理厂生产日志 -->
<template>
  <div class="wrapper">
    <!-- 查询条件 -->
    <CardSearch
      ref="cardSearch"
      :config="cardSearchConfig"
    />
    
    <!-- 数据表格 -->
    <SLCard class="card" title="污水厂监测生产指标表格">
      <template #query>
        <div class="table-title">
          {{ tableTitle }}
        </div>
      </template>
      
      <CardTable
        id="productionLogTable"
        ref="refTable"
        class="card-table"
        :config="cardTableConfig"
      />
    </SLCard>
    
    <!-- 新增/编辑对话框 -->
    <ProductionLogForm
      ref="formDialog"
      @refresh="refreshData"
    />
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, computed, onMounted, shallowRef } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Download, Printer, Plus, Edit, Delete } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import { useBusinessStore } from '@/store'
import { ISearchIns, ICardTableIns } from '@/components/type'
import { printJSON } from '@/utils/printUtils'
import ProductionLogForm from './components/ProductionLogForm.vue'
import {
  getProductionLogList,
  getSewagePlantStationList,
  deleteProductionLog,
  exportProductionLog
} from './api'
import type { ProductionLogData, QueryForm, SewagePlantStation } from './types'

// Store
const businessStore = useBusinessStore()

// Refs
const cardSearch = ref<ISearchIns>()
const refTable = ref<ICardTableIns>()
const formDialog = ref<InstanceType<typeof ProductionLogForm>>()

// State
const state = reactive({
  stationList: [] as SewagePlantStation[],
  loading: false
})

// 表格标题
const tableTitle = computed(() => {
  const queryParams = cardSearch.value?.queryParams as QueryForm
  if (!queryParams) return '污水厂监测生产指标表格'
  
  const stationName = state.stationList.find(s => s.stationId === queryParams.stationId)?.name || '全部站点'
  const dateRange = queryParams.dateRange
  let dateStr = ''
  
  if (dateRange && dateRange[0] && dateRange[1]) {
    const startDate = dayjs(dateRange[0]).format('YYYY-MM-DD')
    const endDate = dayjs(dateRange[1]).format('YYYY-MM-DD')
    dateStr = `(${startDate}至${endDate})`
  }
  
  return `${stationName}监测生产指标表格${dateStr}`
})

// 搜索配置
const cardSearchConfig = reactive<ISearch>({
  defaultParams: {
    pageNum: 1,
    pageSize: 20,
    dateRange: [dayjs().subtract(7, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
  },
  filters: [
    {
      type: 'select',
      field: 'stationId',
      label: '站点选择',
      placeholder: '请选择站点',
      options: computed(() => state.stationList.map(station => ({
        label: station.name || station.stationName,
        value: station.stationId
      }))),
      clearable: true
    },
    {
      type: 'input',
      field: 'operatorName',
      label: '操作员',
      placeholder: '请输入操作员姓名'
    },
    {
      type: 'daterange',
      field: 'dateRange',
      label: '日期范围',
      placeholder: ['开始日期', '结束日期']
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          click: () => refreshData(),
          svgIcon: shallowRef(Search)
        },
        {
          perm: true,
          text: '新增',
          type: 'success',
          click: () => handleAdd(),
          svgIcon: shallowRef(Plus)
        },
        {
          text: '导出',
          perm: true,
          type: 'warning',
          svgIcon: shallowRef(Download),
          click: () => handleExport()
        },
        {
          perm: true,
          text: '打印',
          type: 'info',
          svgIcon: shallowRef(Printer),
          click: () => handlePrint()
        }
      ]
    }
  ]
})

// 表格配置
const cardTableConfig = reactive<ITable>({
  loading: false,
  dataList: [],
  columns: [
    { prop: 'logDate', label: '日期', width: 120, sortable: true },
    { prop: 'stationName', label: '站点名称', minWidth: 150 },
    { prop: 'weather', label: '天气', width: 80 },
    { prop: 'codInlet', label: 'COD mg/L', width: 100, formatter: (row) => row.codInlet?.toFixed(1) || '--' },
    { prop: 'bodInlet', label: 'BOD5 mg/L', width: 100, formatter: (row) => row.bodInlet?.toFixed(1) || '--' },
    { prop: 'suspendedSolidsInlet', label: '悬浮物 mg/L', width: 110, formatter: (row) => row.suspendedSolidsInlet?.toFixed(1) || '--' },
    { prop: 'ammoniaNitrogenInlet', label: '氨氮 mg/L', width: 100, formatter: (row) => row.ammoniaNitrogenInlet?.toFixed(1) || '--' },
    { prop: 'totalNitrogenInlet', label: '总氮 mg/L', width: 100, formatter: (row) => row.totalNitrogenInlet?.toFixed(1) || '--' },
    { prop: 'totalPhosphorusInlet', label: '总磷 mg/L', width: 100, formatter: (row) => row.totalPhosphorusInlet?.toFixed(1) || '--' },
    { prop: 'phInlet', label: '出水PH值', width: 100, formatter: (row) => row.phInlet?.toFixed(1) || '--' },
    { prop: 'flowRate', label: '流量 m³/h', width: 110, formatter: (row) => row.flowRate?.toFixed(1) || '--' },
    { prop: 'operatorName', label: '操作员', width: 100 }
  ],
  operations: [
    {
      perm: true,
      text: '编辑',
      type: 'primary',
      click: (row: ProductionLogData) => handleEdit(row),
      svgIcon: shallowRef(Edit)
    },
    {
      perm: true,
      text: '删除',
      type: 'danger',
      click: (row: ProductionLogData) => handleDelete(row),
      svgIcon: shallowRef(Delete)
    }
  ],
  operationWidth: '150px',
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      cardTableConfig.pagination.page = page
      cardTableConfig.pagination.limit = size
      refreshData()
    }
  }
})

// 加载站点列表
const loadStationList = async () => {
  try {
    const response = await getSewagePlantStationList({
      projectId: businessStore.selectedProject?.value
    })
    
    const dataList = response.data?.data || response.data || []
    
    if (Array.isArray(dataList) && dataList.length > 0) {
      // 过滤出污水厂
      const sewagePlants = dataList.filter((item: any) => {
        const name = item.name || item.stationName || ''
        return name.includes('污水') || name.includes('污水厂') || name.includes('污水处理厂')
      })
      
      state.stationList = sewagePlants.map((item: any) => ({
        id: item.stationId || item.id,
        stationId: item.stationId || item.id,
        name: item.name || item.stationName,
        stationName: item.stationName || item.name,
        location: item.location,
        status: item.status,
        projectId: item.projectId,
        projectName: item.projectName
      }))
    }
  } catch (error) {
    console.error('加载站点列表失败:', error)
    ElMessage.error('加载站点列表失败')
  }
}

// 刷新数据
const refreshData = async () => {
  cardTableConfig.loading = true
  
  try {
    const queryParams = cardSearch.value?.queryParams as QueryForm || {}
    const params: QueryForm = {
      pageNum: cardTableConfig.pagination.page || 1,
      pageSize: cardTableConfig.pagination.limit || 20,
      ...queryParams
    }
    
    const response = await getProductionLogList(params)
    const data = response.data?.data || response.data
    
    if (data) {
      cardTableConfig.dataList = data.list || []
      cardTableConfig.pagination.total = data.total || 0
    }
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    cardTableConfig.loading = false
  }
}

// 新增
const handleAdd = () => {
  formDialog.value?.open()
}

// 编辑
const handleEdit = (row: ProductionLogData) => {
  formDialog.value?.open(row)
}

// 删除
const handleDelete = async (row: ProductionLogData) => {
  try {
    await ElMessageBox.confirm('确定要删除这条生产日志吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await deleteProductionLog([row.id!])
    ElMessage.success('删除成功')
    refreshData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 导出
const handleExport = async () => {
  try {
    const queryParams = cardSearch.value?.queryParams as QueryForm || {}
    await exportProductionLog({
      stationId: queryParams.stationId,
      dateRange: queryParams.dateRange,
      operatorName: queryParams.operatorName,
      format: 'excel'
    })
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  }
}

// 打印
const handlePrint = () => {
  printJSON({
    title: tableTitle.value,
    data: cardTableConfig.dataList,
    titleList: cardTableConfig.columns
  })
}

// 初始化
onMounted(async () => {
  await loadStationList()
  refreshData()
})
</script>

<style lang="scss" scoped>
.wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
}

.card {
  flex: 1;
  
  .table-title {
    font-size: 16px;
    font-weight: 500;
    color: var(--el-text-color-primary);
  }
}

.card-table {
  height: 100%;
}
</style>
