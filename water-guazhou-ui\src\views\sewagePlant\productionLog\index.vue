<!-- 污水处理厂生产日志 -->
<template>
  <div class="wrapper">
    <div
      v-loading="totalLoading"
      class="main"
    >
      <div class="right">
        <CardSearch
          ref="cardSearch"
          :config="cardSearchConfig"
        />
        <CardTable
          id="print"
          ref="refTable"
          class="card-table"
          :config="cardTableConfig"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs'
import { Printer } from '@element-plus/icons-vue'
import { printJSON } from '@/utils/printUtils'
import { getProductionLogList, getSewagePlantStationList } from './api'
import type { ProductionLogData, QueryForm } from './types'
import { useBusinessStore } from '@/store'

const businessStore = useBusinessStore()
const cardSearch = ref()
const refTable = ref()

const state = reactive({
  stationList: [] as any[],
  loading: false
})

const totalLoading = ref(false)

// 查询配置
const cardSearchConfig = reactive({
  queryParams: {
    stationId: '',
    dateRange: null as string[] | null,
    operatorName: ''
  },
  formItems: [
    {
      type: 'select',
      field: 'stationId',
      label: '站点选择',
      placeholder: '请选择站点',
      options: computed(() => state.stationList.map(station => ({
        label: station.name || station.stationName,
        value: station.stationId
      }))),
      clearable: true
    },
    {
      type: 'input',
      field: 'operatorName',
      label: '操作员',
      placeholder: '请输入操作员姓名'
    },
    {
      type: 'daterange',
      field: 'dateRange',
      label: '日期范围',
      placeholder: ['开始日期', '结束日期']
    }
  ],
  operations: [
    {
      label: '查询',
      type: 'primary',
      click: () => refTable.value?.refresh()
    },
    {
      label: '重置',
      click: () => {
        cardSearch.value?.resetForm()
        refTable.value?.refresh()
      }
    }
  ]
})

// 表格配置
const cardTableConfig = reactive({
  title: '污水厂生产日志',
  data: [],
  loading: false,
  pagination: {
    page: 1,
    limit: 20,
    total: 0
  },
  columns: [
    { prop: 'logDate', label: '日期', width: 120, sortable: true },
    { prop: 'stationName', label: '站点名称', width: 150 },
    { prop: 'weather', label: '天气', width: 80 },
    { prop: 'operatorName', label: '操作员', width: 100 },
    {
      prop: 'codInlet',
      label: '进水COD(mg/L)',
      width: 120,
      formatter: (row: ProductionLogData) => row.codInlet
    },
    {
      prop: 'codOutlet',
      label: '出水COD(mg/L)',
      width: 120,
      formatter: (row: ProductionLogData) => row.codOutlet
    },
    {
      prop: 'bodInlet',
      label: '进水BOD5(mg/L)',
      width: 130,
      formatter: (row: ProductionLogData) => row.bodInlet
    },
    {
      prop: 'bodOutlet',
      label: '出水BOD5(mg/L)',
      width: 130,
      formatter: (row: ProductionLogData) => row.bodOutlet
    },
    {
      prop: 'ammoniaNitrogenInlet',
      label: '进水氨氮(mg/L)',
      width: 130,
      formatter: (row: ProductionLogData) => row.ammoniaNitrogenInlet
    },
    {
      prop: 'ammoniaNitrogenOutlet',
      label: '出水氨氮(mg/L)',
      width: 130,
      formatter: (row: ProductionLogData) => row.ammoniaNitrogenOutlet
    },
    {
      prop: 'flowRate',
      label: '流量(m³/h)',
      width: 120,
      formatter: (row: ProductionLogData) => row.flowRate
    },
    { prop: 'remarks', label: '备注', minWidth: 150 }
  ],
  operations: [
    {
      label: '打印',
      icon: shallowRef(Printer),
      click: () => printJSON('print')
    }
  ],
  request: async (params: any) => {
    const queryParams = cardSearch.value?.queryParams as QueryForm || {}
    const requestParams: QueryForm = {
      pageNum: params.page || 1,
      pageSize: params.limit || 20,
      ...queryParams
    }

    const response = await getProductionLogList(requestParams)

    if (response.code === 200 && response.data) {
      return {
        list: response.data.list || [],
        total: response.data.total || 0
      }
    }
    return { list: [], total: 0 }
  }
})

// 加载站点列表
const loadStationList = async () => {
  try {
    const response = await getSewagePlantStationList({
      projectId: businessStore.selectedProject?.value
    })

    const dataList = response.data?.data || response.data || []

    if (Array.isArray(dataList) && dataList.length > 0) {
      const sewagePlants = dataList.filter((item: any) => {
        const name = item.name || item.stationName || ''
        return name.includes('污水') || name.includes('污水厂') || name.includes('污水处理厂')
      })

      state.stationList = sewagePlants.map((item: any) => ({
        stationId: item.stationId || item.id,
        name: item.name || item.stationName,
        stationName: item.stationName || item.name
      }))
    }
  } catch (error) {
    console.error('加载站点列表失败:', error)
  }
}

onMounted(async () => {
  await loadStationList()
})
</script>

<style lang="scss" scoped>
.wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
}

.main {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.right {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.card-table {
  flex: 1;
}
</style>