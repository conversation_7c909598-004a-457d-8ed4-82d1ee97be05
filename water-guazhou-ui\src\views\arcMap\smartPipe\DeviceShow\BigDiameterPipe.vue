<!-- 大口径管线 -->
<template>
  <RightDrawerMap
    ref="refMap"
    title="大口径管线展示"
    @map-loaded="onMaploaded"
  >
    <Form
      ref="refForm"
      :config="FormConfig"
    ></Form>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import { queryLayerClassName } from '@/api/mapservice'
import { IFormIns } from '@/components/type'
import { getLayerOids, getSubLayerIds } from '@/utils/MapHelper'
import RightDrawerMap from '../../components/common/RightDrawerMap.vue'
import { SLMessage } from '@/utils/Message'
import { QueryByPolygon } from '@/utils/geoserver/wfsUtils'
// import RangeSelector from '@/components/Form/RangeSelecter.vue'

const refForm = ref<IFormIns>()
const refMap = ref<InstanceType<typeof RightDrawerMap>>()
const staticState: {
  view?: __esri.MapView
  graphicsLayer?: __esri.GraphicsLayer
} = {}
const state = reactive<Record<string, any>>({
  tabs: [],
  loading: false,
  layerIds: [],
  layerInfos: []
})
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fieldset: {
        desc: '管径范围'
      },
      fields: [
        {
          type: 'range',
          rangeType: 'input',
          field: 'range',
          // options: [
          //   { label: '50', value: 50 },
          //   { label: '100', value: 100 },
          //   { label: '300', value: 300 },
          //   { label: '500', value: 500 },
          //   { label: '800', value: 800 },
          //   { label: '1000', value: 1000 }
          // ],
          startPlaceHolder: '0',
          endPlaceHolder: '1000+',
          startOptionDisabled: (option, end) => {
            return end && Number(end) < option.value
          },
          endOptionDisabled: (option, start) => {
            return start && option.value < Number(start)
          }
        }
      ]
    },
    {
      fieldset: {
        desc: '选择图层'
      },
      fields: [
        {
          type: 'tree',
          options: [],
          checkStrictly: true,
          showCheckbox: true,
          field: 'layerid',
          nodeKey: 'value'
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              styles: { width: '100%' },
              text: '展示',
              loading: () => state.loading,
              click: () => handleQuery()
            }
          ]
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12,
  defaultValue: {
    min: '',
    max: ''
  }
})

const getLayerInfo = () => {
  const field = FormConfig.group[1].fields[0] as IFormTree;
  const layerInfo = staticState.view?.layerViews.items[0].layer.sublayers;
  let layers = layerInfo.items.filter(item => item.type && item.type.toLowerCase().includes('line'))
  .map(item => ({
    label: item.name,
    value: item.name,
    layername: item.name,
    type: item.type,
    spatialReferences: item.spatialReferences
  }));
  field.options = layers;// [{ label: '管线类', value: -2, children: layers }]  
}
const handleQuery = async () => {
  state.loading = true
  try {
    const { range, layerid } = refForm.value?.dataForm || {}
    if (!layerid?.length) {
      SLMessage.warning('请选择管线图层')
    } else {
      const [min, max] = range || []
      const allResults: any[] = []
      for (const typeName of layerid) {
        let where = '1=1'
        if (min && max) {
          where += ` AND "管径" >= ${min} AND "管径" <= ${max}`
        } else if (min) {
          where += ` AND "管径" >= ${min}`
        } else if (max) {
          where += ` AND "管径" <= ${max}`
        }
        const res = await QueryByPolygon(typeName, undefined, where)
        let features = res.data?.features || []
        allResults.push({
          name: typeName,
          data: features
        })
      }
      state.tabs = allResults
      refMap.value?.refreshDetail(state.tabs)
    }
  } catch (error: any) {
    SLMessage.error(error.message)
  }
  state.loading = false
}
const onMaploaded = async view => {
  staticState.view = view
  setTimeout(()=>{
    getLayerInfo()
  },1000)
}
</script>
<style lang="scss" scoped></style>
