<template>
  <div class="app-container">
    <div class="remote-control-container">
      <!-- 左侧水厂选择 -->
      <div class="water-plant-container">
        <div class="water-plant-title">水厂选择</div>
        <div class="water-plant-content">
          <el-tree
            ref="waterPlantTree"
            :data="waterPlantTreeData"
            :props="waterPlantTreeProps"
            node-key="id"
            highlight-current
            default-expand-all
            :current-node-key="currentWaterPlantId"
            @node-click="handleWaterPlantSelect"
          ></el-tree>
        </div>
      </div>

      <!-- 右侧设备列表 -->
      <div class="device-list-container">
        <!-- 搜索表单 -->
        <el-form :model="queryParams" ref="queryFormRef" :inline="true" class="search-form">
          <el-form-item label="设备名称：">
            <el-input v-model="queryParams.deviceName" placeholder="请输入设备名称" clearable></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">查询</el-button>
            <el-button @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <!-- 数据表格 -->
        <el-table
          v-loading="loading"
          :data="deviceList"
          border
          :row-key="(row) => extractDeviceId(row)"
        >
          <el-table-column label="设备名称" prop="name" align="center"></el-table-column>
          <el-table-column label="设备类型" align="center">
            <template #default="scope">
              <el-tag :type="getDeviceTypeTagType(scope.row.deviceTypeName)">
                {{ getDeviceTypeName(scope.row.deviceTypeName) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="安装位置" prop="location" align="center"></el-table-column>

          <el-table-column label="操作" align="center" width="200">
            <template #default="scope">
              <div style="display: flex; justify-content: center; gap: 10px;">
                <el-button type="primary" size="small" @click="handleRemoteControl(scope.row)">
                  远程控制
                </el-button>
                <el-button type="success" size="small" @click="handleViewRecords(scope.row)">
                  查看记录
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="queryParams.page"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="queryParams.size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          ></el-pagination>
        </div>
      </div>
    </div>

    <!-- 控制记录对话框 -->
    <ControlRecordDialog
      v-model="controlDialogVisible"
      :device-info="currentDevice"
      :mode="dialogMode"
      :record-data="currentRecord"
      @submit="handleControlSubmit"
      @cancel="handleControlCancel"
    />

    <!-- 查看控制记录对话框 -->
    <el-dialog
      title="控制记录"
      v-model="recordsDialogVisible"
      width="80%"
      append-to-body
    >
      <el-table :data="controlRecords" border v-loading="loadingControlRecords" :key="recordsDialogVisible ? 'open' : 'closed'">
        <el-table-column label="控制类型" prop="controlType" align="center">
          <template #default="scope">
            <el-tag :type="getControlTypeTagType(scope.row.controlType)">
              {{ getControlTypeName(scope.row.controlType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="控制描述" prop="controlDescription" align="center"></el-table-column>
        <el-table-column label="控制值" prop="controlValue" align="center">
          <template #default="scope">
            {{ scope.row.controlValue || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="操作人员" prop="operatorName" align="center"></el-table-column>
        <el-table-column label="控制时间" prop="controlTime" align="center">
          <template #default="scope">
            {{ formatDateTime(scope.row.controlTime) }}
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="status" align="center">
          <template #default="scope">
            <el-tag :type="getControlStatusTagType(scope.row.status)">
              {{ getControlStatusName(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" align="center"></el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, reactive, onMounted, toRefs, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import type { FormInstance } from 'element-plus';
import {
  getDeviceControlRecords
} from '@/api/remoteControl';
import { getMqttDeviceList } from '@/api/deviceAuth';
import { formatDate } from '@/utils/DateFormatter';
import { getProjectRoot } from '@/api/company_org';
import ControlRecordDialog from './components/ControlRecordDialog.vue';


// 设备类型映射
const DEVICE_TYPE_MAP = {
  pump: '水泵',
  valve: '阀门',
  monitor: '监测设备',
  controller: '控制器'
};

// 控制类型映射
const CONTROL_TYPE_MAP = {
  start: '启动',
  stop: '停止',
  open: '开启',
  close: '关闭',
  adjust: '调节',
  reset: '重置'
};


// 控制状态映射
const CONTROL_STATUS_MAP = {
  pending: '待执行',
  success: '成功',
  failed: '失败',
  cancelled: '已取消'
};

interface DeviceItem {
  id: string;
  name: string;
  code: string;
  type: string;
  waterPlantId: string;
  waterPlantName: string;
  location: string;
  status: number;
}

export default defineComponent({
  name: 'RemoteDeviceControl',
  components: {
    ControlRecordDialog
  },
  setup() {
    const queryFormRef = ref<FormInstance>();
    const waterPlantTree = ref();


    const state = reactive({
      // 水厂树数据
      waterPlantTreeData: [] as any[],
      // 水厂树配置
      waterPlantTreeProps: {
        label: 'name',
        children: 'children'
      },
      // 选中的水厂
      selectedWaterPlant: null as any,
      // 当前选中的水厂ID（用于树组件高亮显示）
      currentWaterPlantId: '',
      // 查询参数
      queryParams: {
        page: 1,
        size: 10,
        waterPlantId: '',
        deviceName: '',
        deviceCode: '',

      },
      // 加载状态
      loading: false,
      // 设备列表
      deviceList: [] as DeviceItem[],
      // 总记录数
      total: 0,
      // 当前设备
      currentDevice: null as any,
      // 当前记录
      currentRecord: null as any,
      // 控制对话框可见性
      controlDialogVisible: false,
      // 记录查看对话框可见性
      recordsDialogVisible: false,
      // 控制记录列表
      controlRecords: [] as any[],
      // 控制记录加载状态
      loadingControlRecords: false,
      // 对话框模式
      dialogMode: 'add' as 'add' | 'edit'
    });

    // 获取设备类型名称
    const getDeviceTypeName = (type: string) => {
      return DEVICE_TYPE_MAP[type] || type;
    };

    // 获取设备类型标签类型
    const getDeviceTypeTagType = (type: string) => {
      const typeMap = {
        pump: 'primary',
        valve: 'success',
        monitor: 'warning',
        controller: 'info'
      };
      return typeMap[type] || 'info';
    };

    // 获取控制类型名称
    const getControlTypeName = (type: string) => {
      return CONTROL_TYPE_MAP[type] || type;
    };

    // 获取控制类型标签类型
    const getControlTypeTagType = (type: string) => {
      const typeMap = {
        start: 'success',
        stop: 'danger',
        open: 'primary',
        close: 'warning',
        adjust: 'info',
        reset: 'default'
      };
      return typeMap[type] || 'default';
    };

    // 获取控制状态名称
    const getControlStatusName = (status: string) => {
      return CONTROL_STATUS_MAP[status] || status;
    };

    // 获取控制状态标签类型
    const getControlStatusTagType = (status: string) => {
      const statusMap = {
        pending: 'warning',
        success: 'success',
        failed: 'danger',
        cancelled: 'info'
      };
      return statusMap[status] || 'info';
    };

    // 格式化日期时间
    const formatDateTime = (dateTime: string) => {
      if (!dateTime) return '-';
      return formatDate(dateTime, 'YYYY-MM-DD HH:mm:ss');
    };

    // 提取设备ID的辅助函数（与设备权限页面保持一致）
    const extractDeviceId = (device: any): string => {
      if (typeof device.id === 'object' && device.id?.id) {
        return device.id.id;
      }
      return device.id || '';
    };

    // 获取第一个子水厂（第一个有子节点的节点的第一个子节点）（与设备权限页面完全一致）
    const getFirstChildProject = (treeData: any[]): any => {
      for (const node of treeData) {
        // 如果当前节点有子节点，返回第一个子节点
        if (node.children && Array.isArray(node.children) && node.children.length > 0) {
          return node.children[0];
        }
      }
      // 如果没有找到有子节点的节点，返回第一个节点
      return treeData.length > 0 ? treeData[0] : null;
    };

    // 获取水厂树数据
    const getWaterPlantTreeData = async () => {
      try {
        // 使用项目根接口获取水厂列表（与设备权限页面保持一致）
        const res = await getProjectRoot();
        // 处理API返回的数据结构（与设备权限页面完全一致）
        if (Array.isArray(res.data)) {
          state.waterPlantTreeData = res.data;
        } else if (res.data?.data && Array.isArray(res.data.data)) {
          state.waterPlantTreeData = res.data.data;
        } else {
          state.waterPlantTreeData = [];
        }

        // 默认选择第一个子水厂（与设备权限页面保持一致）
        const firstChildProject = getFirstChildProject(state.waterPlantTreeData);

        if (firstChildProject) {
          state.selectedWaterPlant = firstChildProject;
          state.currentWaterPlantId = firstChildProject.id;
          state.queryParams.waterPlantId = firstChildProject.id;

          // 等待DOM更新后设置树组件的当前节点，确保高亮显示
          nextTick(() => {
            if (waterPlantTree.value) {
              waterPlantTree.value.setCurrentKey(firstChildProject.id);
            }
          });

          // 自动加载该项目的设备列表
          getList();
        }
      } catch (error) {
        console.error('获取水厂列表失败', error);
        ElMessage.error('获取水厂列表失败');
        state.waterPlantTreeData = [];
      }
    };

    // 获取设备列表
    const getList = async () => {
      state.loading = true;
      try {
        const params = { ...state.queryParams };

        // 如果选中了水厂，添加水厂ID过滤
        if (state.selectedWaterPlant) {
          params.waterPlantId = state.selectedWaterPlant.id;
        }

        // 使用MQTT设备API获取设备列表（与设备权限页面保持一致）
        // 如果没有选择项目，使用默认项目ID或提示用户选择
        const projectId = state.currentWaterPlantId || state.queryParams.waterPlantId;

        if (!projectId) {
          ElMessage.warning('请先选择水厂');
          state.deviceList = [];
          state.total = 0;
          state.loading = false;
          return;
        }

        const res = await getMqttDeviceList(projectId, {
          page: state.queryParams.page || 1,
          size: state.queryParams.size || 10,
          // 如果有设备名称搜索，传递给后端
          deviceName: state.queryParams.deviceName || ''
        });

        let deviceList: any[] = [];
        if (res.data?.data?.data) {
          deviceList = res.data.data.data || [];
          state.total = res.data.data.total || 0;
        } else if (res.data?.data) {
          deviceList = Array.isArray(res.data.data) ? res.data.data : [];
          state.total = res.data.total || deviceList.length;
        } else if (Array.isArray(res.data)) {
          deviceList = res.data;
          state.total = res.data.length || 0;
        } else {
          deviceList = [];
          state.total = 0;
        }

        // 初始化设备列表，为每个设备添加控制记录相关属性
        state.deviceList = deviceList.map(device => ({
          ...device,
          controlRecords: [],
          loadingControlRecords: false,
          controlRecordsLoaded: false
        }));
      } catch (error) {
        console.error('获取设备列表失败', error);
        ElMessage.error('获取设备列表失败');
      } finally {
        state.loading = false;
      }
    };



    // 水厂选择事件
    const handleWaterPlantSelect = (data: any) => {
      state.selectedWaterPlant = data;
      state.currentWaterPlantId = data.id;
      state.queryParams.page = 1;
      getList();
    };

    // 查询按钮点击事件
    const handleQuery = () => {
      state.queryParams.page = 1;
      getList();
    };

    // 重置按钮点击事件
    const resetQuery = () => {
      queryFormRef.value?.resetFields();
      state.queryParams.deviceName = '';
      state.queryParams.deviceCode = '';

      handleQuery();
    };

    // 每页条数变化事件
    const handleSizeChange = (val: number) => {
      state.queryParams.size = val;
      getList();
    };

    // 当前页变化事件
    const handleCurrentChange = (val: number) => {
      state.queryParams.page = val;
      getList();
    };



    // 远程控制按钮点击事件
    const handleRemoteControl = (row: DeviceItem) => {
      state.currentDevice = row;
      state.currentRecord = null;
      state.dialogMode = 'add';
      state.controlDialogVisible = true;
    };

    // 获取控制记录用于对话框显示
    const getControlRecordsForDialog = async (device: DeviceItem) => {
      state.loadingControlRecords = true;
      try {
        // 调用API获取控制记录
        const res = await getDeviceControlRecords(extractDeviceId(device));
        // 处理不同的响应格式
        let records: any[] = [];
        if (res?.data?.data && Array.isArray(res.data.data)) {
          // 格式: { data: { data: [...] } }
          records = res.data.data;
        } else if (res?.data && Array.isArray(res.data)) {
          // 格式: { data: [...] }
          records = res.data;
        } else if (Array.isArray(res)) {
          // 格式: [...]
          records = res;
        } else {
          console.warn('未知的响应格式:', res);
          records = [];
        }

        state.controlRecords = records;
      } catch (error) {
        console.error('获取控制记录失败', error);
        state.controlRecords = [];
        ElMessage.error('获取控制记录失败');
      } finally {
        state.loadingControlRecords = false;
      }
    };

    // 查看记录按钮点击事件
    const handleViewRecords = (row: DeviceItem) => {
      // 先清空控制记录数据
      state.controlRecords = [];
      // 设置当前设备
      state.currentDevice = row;
      // 打开控制记录查看对话框
      state.recordsDialogVisible = true;
      // 异步获取控制记录数据
      nextTick(() => {
        getControlRecordsForDialog(row);
      });
    };

    // 新增控制记录按钮点击事件
    const handleAddControlRecord = (row: DeviceItem) => {
      handleRemoteControl(row);
    };



    // 处理控制提交（子组件已经处理了API调用，这里只需要刷新数据）
    const handleControlSubmit = () => {
      // 子组件已经处理了API调用和成功/失败消息
      // 这里只需要刷新设备列表
      state.controlDialogVisible = false;
      getList();
    };

    // 处理控制取消
    const handleControlCancel = () => {
      state.controlDialogVisible = false;
      state.currentDevice = null;
      state.currentRecord = null;
    };

    onMounted(() => {
      getWaterPlantTreeData();
      // 不在初始化时调用getList，等水厂选择完成后再调用
    });

    return {
      queryFormRef,
      waterPlantTree,
      ...toRefs(state),
      extractDeviceId,
      getDeviceTypeName,
      getDeviceTypeTagType,
      getControlTypeName,
      getControlTypeTagType,
      getControlStatusName,
      getControlStatusTagType,
      formatDateTime,
      handleWaterPlantSelect,
      handleQuery,
      resetQuery,
      handleSizeChange,
      handleCurrentChange,

      handleRemoteControl,
      handleViewRecords,
      handleAddControlRecord,

      handleControlSubmit,
      handleControlCancel
    };
  }
});
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.remote-control-container {
  display: flex;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  min-height: calc(100vh - 120px);
}

.water-plant-container {
  width: 280px;
  border-right: 1px solid #e6e6e6;
  padding: 20px 0;
  display: flex;
  flex-direction: column;
}

.water-plant-title {
  font-size: 16px;
  font-weight: bold;
  padding: 0 20px 15px;
  border-bottom: 1px solid #e6e6e6;
  margin-bottom: 15px;
}

.water-plant-content {
  padding: 0 20px;
  flex: 1;
  overflow-y: auto;
}

.device-list-container {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.search-form {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.sub-table {
  margin: 10px 0;
  border-radius: 4px;
}

.expand-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding: 0 10px;
}

.expand-title {
  font-size: 14px;
  font-weight: bold;
  color: #606266;
}



/* 设备类型标签样式 */
.el-tag.el-tag--primary {
  background-color: #e6f3ff;
  border-color: #b3d8ff;
  color: #409eff;
}

.el-tag.el-tag--success {
  background-color: #f0f9ff;
  border-color: #c0e6c0;
  color: #67c23a;
}

.el-tag.el-tag--warning {
  background-color: #fdf6ec;
  border-color: #f5dab1;
  color: #e6a23c;
}

.el-tag.el-tag--danger {
  background-color: #fef0f0;
  border-color: #fbc4c4;
  color: #f56c6c;
}

.el-tag.el-tag--info {
  background-color: #f4f4f5;
  border-color: #d3d4d6;
  color: #909399;
}

/* 表格样式优化 */
.el-table {
  border-radius: 4px;
  overflow: hidden;
}

.el-table .el-table__header-wrapper {
  background-color: #fafafa;
}

.el-table .el-table__header th {
  background-color: #fafafa;
  color: #606266;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .remote-control-container {
    flex-direction: column;
  }

  .water-plant-container {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #e6e6e6;
    padding: 15px 20px;
    max-height: 200px;
  }
}

@media (max-width: 768px) {
  .search-form .el-form-item {
    width: 100%;
    margin-bottom: 15px;
  }

  .device-info p {
    font-size: 12px;
  }
}
</style>
