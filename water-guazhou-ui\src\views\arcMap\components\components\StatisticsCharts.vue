<template>
  <div class="detail-wrapper">
    <div v-if="!tabs?.length" class="empty">暂无结果</div>
    <template v-else>
      <div class="statistics-tab-wrapper">
        <div class="tab-wrapper">
          <Form ref="refTab" :config="FormConfig"></Form>
          <div v-if="state.curTab === '1'" class="filter-box">
            <FormTableColumnFilter
              :columns="TableConfig.columns"
              :show-tooltip="true"
            ></FormTableColumnFilter>
          </div>
        </div>
        <div class="content">
          <div class="tabs overlay-y">
            <div
              v-for="(tab, i) in state.tabs"
              :key="i"
              class="tab-item"
              :class="[state.curTab === tab.value ? 'active' : '']"
              @click="state.curTab = tab.value"
            >
              {{ tab.label }}
            </div>
          </div>
          <div class="right">
            <div v-show="state.curTab === '1'" class="statistics-table">
              <FormTable :config="TableConfig"></FormTable>
            </div>
            <div
              v-show="state.curTab === '0'"
              class="statistics-chart"
              style="height: 100%"
            >
              <div class="attr-table-container">
                <div class="attr-table-box">
                  <el-table
                    :data="state.dimensionTableData"
                    style="width: 100%"
                    size="small"
                    @row-click="handleDimensionTableRowClick"
                    row-key="fieldName"
                    :expand-row-keys="state.expandedRows"
                    @expand-change="handleExpandChange"
                  >
                    <el-table-column type="expand">
                      <template #default="{ row }">
                        <div class="dimension-detail">
                          <div class="detail-header">
                            <span class="detail-title"
                              >{{ row.fieldName }}分布详情</span
                            >
                            <span class="detail-count"
                              >共{{ row.details.length }}类</span
                            >
                          </div>
                          <div class="detail-items">
                            <div
                              v-for="(item, index) in row.details"
                              :key="index"
                              class="detail-item"
                              @click="
                                handleDetailItemClick(row.fieldName, item)
                              "
                            >
                              <div class="item-info">
                                <span class="item-name">{{ item.name }}</span>
                                <span class="item-count"
                                  >{{ item.count }}{{ unit }}</span
                                >
                              </div>
                              <div class="item-progress">
                                <div class="progress-bar">
                                  <div
                                    class="progress-fill"
                                    :style="{ width: item.percentage + '%' }"
                                  ></div>
                                </div>
                                <span class="percentage"
                                  >{{ item.percentage }}%</span
                                >
                              </div>
                            </div>
                          </div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="fieldName"
                      label="统计维度"
                      min-width="100"
                    >
                      <template #default="{ row }">
                        <div class="dimension-name">
                          <el-icon
                            v-if="row.fieldName === state.selectedDimension"
                            class="selected-icon"
                          >
                            <Check />
                          </el-icon>
                          <span>{{ row.fieldName }}</span>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="total"
                      label="总数"
                      width="80"
                      align="right"
                    >
                      <template #default="{ row }">
                        <span class="total-count"
                          >{{ row.total }}{{ unit }}</span
                        >
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="types"
                      label="类型数"
                      width="80"
                      align="right"
                    >
                      <template #default="{ row }">
                        <span class="type-count">{{ row.types }}类</span>
                      </template>
                    </el-table-column>
                  </el-table>

                  <!-- 总计信息 -->
                  <div class="table-summary">
                    <div class="summary-item">
                      <span class="summary-label">数据总计:</span>
                      <span class="summary-value"
                        >{{ state.totalCount }}{{ unit }}</span
                      >
                    </div>
                  </div>
                </div>
              </div>
              <div class="charts-container">
                <div class="bar-container">
                  <div class="bar">
                    <VChart
                      ref="refBar"
                      :option="state.barOption"
                      @click="handleBarClick"
                    ></VChart>
                  </div>
                </div>
                <div class="ring-container">
                  <div class="ring">
                    <VChart
                      ref="refRing"
                      :option="state.ringOption"
                      @click="handleRingClick"
                    ></VChart>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>
<script lang="ts" setup>
import { useDetector } from '@/hooks/echarts';
import { IECharts } from '@/plugins/echart';
import { useAppStore, useGisStore } from '@/store';
import { formatDate } from '@/utils/DateFormatter';
import { formatterDate, transNumberUnit } from '@/utils/GlobalHelper';
import {
  EStatisticField,
  excuteQuery,
  getGraphicLayer,
  gotoAndHighLight,
  setSymbol
} from '@/utils/MapHelper';
import { convertGeoJSONToArcGIS } from '@/utils/geoserver/geoserverUtils';
import { SLMessage } from '@/utils/Message';
import { createBarChart, createPieChart } from '@/utils/chartConfig';
import { skipedFields } from '../../smartPipe/PipeManage/config';
import { Check } from '@element-plus/icons-vue';
import { useHighLight } from '@/hooks/arcgis/useHighLight';
import Graphic from '@arcgis/core/Graphic.js';
import { zhuzhuangtu } from '@/views/workorder/statistics/echart';

const ring = (
  data: {
    name: string;
    nameAlias?: string;
    value: string;
    valueAlias?: string;
    scale: string;
  }[] = [],
  unit?: string,
  prefix?: string,
  percision = 2
) => {
  const title = '总数';
  const formatNumber = function (num) {
    const reg = /(?=(\B)(\d{3})+$)/g;
    return num.toString().replace(reg, ',');
  };
  const total = data.reduce((a, b: any) => {
    return a + (parseFloat(b.value) || 0) * 1;
  }, 0);
  const transedTotal = transNumberUnit(total);
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        return (
          (prefix || '') +
          params.name +
          ': ' +
          Number(params.value).toFixed(percision) +
          ' ' +
          unit
        );
      }
    },
    legend: {
      // selectedMode: false, // 取消图例上的点击事件
      type: 'scroll',
      icon: 'circle',
      orient: 'vertical',
      left: 'right',
      top: 'center',
      align: 'left',
      itemGap: 10,
      itemWidth: 10, // 设置宽度
      itemHeight: 10, // 设置高度
      symbolKeepAspect: true,
      textStyle: {
        color: '#fff',
        rich: {
          name: {
            align: 'left',
            width: 60,
            fontSize: 12,
            color: useAppStore().isDark ? '#fff' : '#2A2A2A'
          },
          value: {
            align: 'left',
            width: 60,
            fontSize: 12,
            color: '#00ff00'
          },
          count: {
            align: 'left',
            width: 60,
            fontSize: 12
          },
          upRate: {
            align: 'left',
            fontSize: 12
          },
          downRate: {
            align: 'left',
            fontSize: 12,
            color: '#409EFF'
          }
        }
      },
      data: data.map((item) => item.name),
      formatter(name) {
        if (data && data.length) {
          for (let i = 0; i < data.length; i++) {
            const scale = data[i].scale.substr(0, data[i].scale.length - 1);
            if (name === data[i].name) {
              return (
                '{name| ' +
                (data[i].nameAlias || name) +
                '}' +
                '{value| ' +
                (data[i].valueAlias || data[i].value) +
                ' ' +
                (unit || '') +
                '}' +
                '{downRate| ' +
                (formatNumber(Number(scale || '0').toFixed(percision)) + '%' ||
                  '') +
                '}'
              );
            }
          }
        }
      }
    },
    title: [
      {
        text:
          '{name|' +
          title +
          ((unit && '(' + transedTotal.unit + unit + ')') ||
            '(' + transedTotal.unit + ')') +
          '}\n{val|' +
          formatNumber(transedTotal.value.toFixed(percision)) +
          '}',
        top: 'center',
        left: '19%',
        textAlign: 'center',
        textStyle: {
          rich: {
            name: {
              fontSize: 10,
              fontWeight: 'normal',
              padding: [8, 0],
              align: 'center',
              color: useAppStore().isDark ? '#fff' : '#2A2A2A'
            },
            val: {
              fontSize: 16,
              fontWeight: 'bold',
              color: useAppStore().isDark ? '#fff' : '#2A2A2A'
            }
          }
        }
      }
    ],
    series: [
      {
        type: 'pie',
        radius: ['45%', '60%'],
        center: ['20%', '50%'],
        data,
        hoverAnimation: true,
        label: {
          show: false,
          formatter: (params) => {
            return (
              '{icon|●}{name|' +
              params.name +
              '}{value|' +
              formatNumber(Number(params.value || '0').toFixed(percision)) +
              '}'
            );
          },
          padding: [0, -100, 25, -100],
          rich: {
            icon: {
              fontSize: 16
            },
            name: {
              fontSize: 14,
              padding: [0, 10, 0, 4]
            },
            value: {
              fontSize: 18,
              fontWeight: 'bold'
            }
          }
        }
      }
    ]
  };
  return option;
};

const refBar = ref<IECharts>();
const refRing = ref<IECharts>();
const refTab = ref<IFormIns>();
const emit = defineEmits([
  'rowClick',
  'ring-click',
  'bar-click',
  'attr-row-click',
  'detail-refreshing',
  'detail-refreshed',
  'total-row-click'
]);
const props = defineProps<{
  view?: __esri.MapView;
  layerIds?: number[];
  queryParams: {
    where?: string;
    geometry?: any;
  };
  statisticsParams: {
    group_fields: string[];
    statistic_field: EStatisticField;
    /** 1:数量; 2：长度 */
    statistic_type: '1' | '2';
  };

  tabs?: { label: string; name: string; data?: any }[];
  unit?: string;
  allDevice?: boolean;
  prefix?: string;
  percision?: number;
  theme?: 'darkblue' | 'dark' | 'light';
}>();
const state = reactive<{
  tabs: { label: string; value: string }[];
  curTab: string;
  attributes: { label: string; value: string; data?: any; notLink?: boolean }[];
  ringOption: any;
  barOption: any;
  fieldStats: Map<string, Map<string, number>>;
  selectedDimension: string;
  dimensionTableData: Array<{
    fieldName: string;
    total: number;
    types: number;
    details: Array<{
      name: string;
      count: number;
      percentage: number;
    }>;
  }>;
  expandedRows: string[];
  totalCount: number;
}>({
  attributes: [],
  ringOption: null,
  barOption: null,
  tabs: [
    { label: '统计', value: '0' },
    { label: '详情', value: '1' }
  ],
  curTab: '0',
  fieldStats: new Map(),
  selectedDimension: '',
  dimensionTableData: [],
  expandedRows: [],
  totalCount: 0
});
const staticState: {
  fieldConfig?: any;
  hilightLayer?: __esri.GraphicsLayer;
  tabFeatures: any[];
  currentTabData?: any[];
} = {
  tabFeatures: [],
  currentTabData: []
};

// 初始化高亮hook
const { highlight, removeHoverHighLight } = useHighLight();
const FormConfig = reactive<IFormConfig>({
  labelPosition: 'top',
  gutter: 16,
  group: [
    {
      fields: [
        {
          type: 'tabs',
          field: 'type',
          tabs: [],
          onChange: (val) => {
            console.log('一级Tab切换到:', val);
            // 记录用户选择的Tab
            currentSelectedTab = val;
            console.log('记录当前选择的Tab:', currentSelectedTab);

            // 一级Tab切换时重置筛选状态
            if (isFiltering) {
              console.log('一级Tab切换，重置筛选状态');
              isFiltering = false;
            }
            refreshDetail(val, true);
          }
        }
      ]
    }
  ]
});
const TableConfig = reactive<ITable>({
  maxHeight: 300,
  dataList: [],
  columns: [{ label: '序号', prop: 'index' }],
  pagination: {
    refreshData: async ({ page, size }) => {
      TableConfig.pagination.page = page;
      TableConfig.pagination.limit = size;

      const tab = refTab.value?.dataForm?.type;
      // 直接刷新表格，不再需要layerindex
      tab && refreshTable(tab, null);
    }
  },
  handleRowClick: (row) => {
    console.log('点击表格行:', row);
    TableConfig.currentRow = row;

    // 高亮单个要素
    if (row.OBJECTID) {
      highlightSingleFeature(row.OBJECTID);
    }

    if (props.view) {
      extentTo(props.view, refTab.value?.dataForm?.type, row.OBJECTID);
    } else {
      emit('rowClick', row, refTab.value?.dataForm?.type);
    }
  }
});
// const handleRingClick = params => {
//   state.curTab = '1'
//   emit('ring-click', params, refTab.value?.dataForm?.type)
// }
// const handleBarClick = params => {
//   state.curTab = '1'
//   emit('bar-click', params, refTab.value?.dataForm?.type)
// }

const refreshDetail = async (tab?: string, showAll?: boolean) => {
  debugger
  // 如果正在筛选状态，不要重置筛选状态和刷新表格
  if (isFiltering) {
    // 只处理统计相关的逻辑
    if (!showAll) return;
    if (props.allDevice) {
      refreshAllDeviceStatistics();
    } else {
      refreshStatistics();
    }
    return;
  }

  // 重置筛选状态
  isFiltering = false;

  // 修复：确保详情表格总是能加载数据
  // 当切换到详情Tab时，即使showAll为false，也要加载表格数据
  const shouldLoadTable = showAll || state.curTab === '1';
  refreshTable(tab, null, shouldLoadTable);

  if (!showAll) return;
  if (props.allDevice) {
    refreshAllDeviceStatistics();
  } else {
    refreshStatistics();
  }
};
const setCurLayer = (tab: string) => {
  refTab.value?.dataForm && (refTab.value.dataForm.type = tab);
};

/**
 * 根据选中的维度更新图表数据
 */
const updateChartsForDimension = (dimension: string) => {
  if (!dimension || !state.fieldStats.has(dimension)) {
    state.barOption = null;
    state.ringOption = null;
    return;
  }
  // 根据单位判断统计类型
  const isLengthStatistic = props.unit === 'm' || props.unit === '米';
  const valueMap = state.fieldStats.get(dimension)!;
  // 构建柱状图数据
  const barData = Array.from(valueMap.entries()).map(([key, value]) => ({
    value: isLengthStatistic ? value.toFixed(1) : value,
    key: key,
    label: key
  }));

  // 构建环形图数据
  const ringData = Array.from(valueMap.entries()).map(([key, value]) => ({
    value: value.toFixed(1),
    name: key,
    scale: '0%'
  }));

  const total = Array.from(valueMap.values()).reduce(
    (sum, value) => sum + value,
    0
  );

  // 计算百分比
  ringData.forEach((item) => {
    const numValue = parseInt(item.value);
    item.scale =
      total === 0
        ? '0%'
        : ((numValue / total) * 100).toFixed(props.percision || 1) + '%';
  });

  // 使用统一的图表配置
  state.barOption = createBarChart(barData, {
    title: dimension ? `${dimension}统计分布` : '统计分布',
    unit: props.unit,
    showLabel: true
  });

  state.ringOption = createPieChart(
    ringData.map((item) => ({ name: item.name, value: parseInt(item.value) })),
    {
      title: dimension ? `${dimension}占比分布` : '占比分布',
      unit: props.unit,
      showLegend: true,
      showCenter: true
    }
  );
};

/**
 * 通用高亮方法
 * @param type 高亮类型：'all' | 'filter' | 'single'
 * @param options 高亮选项
 */
const highlightFeatures = async (
  type: 'all' | 'filter' | 'single',
  options?: {
    fieldName?: string;
    fieldValue?: string;
    objectId?: number;
  }
) => {
  if (!props.view || !staticState.currentTabData?.length) {
    console.log('无法高亮：缺少地图视图或数据');
    return;
  }

  try {
    // 清除之前的高亮
    removeHoverHighLight();

    // 根据类型确定数据源和样式
    let dataToHighlight: any[] = [];
    let symbolOptions: any = {};
    let layerId = 'statistics-highlight';
    let shouldZoom = false;

    switch (type) {
      case 'all':
        dataToHighlight = staticState.currentTabData;
        symbolOptions = {
          color: [255, 255, 0, 0.8], // 黄色高亮
          width: 4,
          outlineColor: [255, 0, 0, 1], // 红色边框
          outlineWidth: 2
        };
        break;

      case 'filter':
        if (!options?.fieldName || !options?.fieldValue) {
          console.error('筛选高亮需要提供fieldName和fieldValue');
          return;
        }
        dataToHighlight = staticState.currentTabData.filter((item: any) => {
          return item[options.fieldName!] === options.fieldValue;
        });
        symbolOptions = {
          color: [0, 255, 255, 0.8], // 青色高亮
          width: 4,
          outlineColor: [255, 0, 0, 1], // 红色边框
          outlineWidth: 2
        };
        console.log(
          `筛选条件: ${options.fieldName} = ${options.fieldValue}，找到${dataToHighlight.length}个要素`
        );
        break;

      case 'single':
        if (!options?.objectId) {
          return;
        }
        const targetFeature = staticState.currentTabData.find((item: any) => {
          return item.OBJECTID === options.objectId;
        });
        dataToHighlight = targetFeature ? [targetFeature] : [];
        symbolOptions = {
          color: [255, 0, 255, 0.8], // 紫色高亮
          width: 5,
          outlineColor: [255, 255, 0, 1], // 黄色边框
          outlineWidth: 3
        };
        layerId = 'temp-highlight-layer';
        shouldZoom = true;
        console.log(`高亮单个要素 OBJECTID: ${options.objectId}`);
        break;
    }

    if (dataToHighlight.length === 0) {
      console.warn('没有找到要高亮的要素');
      return;
    }

    // 获取或创建高亮图层
    const layer = getGraphicLayer(props.view, {
      id: layerId,
      title: type === 'single' ? '临时高亮' : '统计高亮'
    });

    if (!layer) {
      console.error('无法创建高亮图层');
      return;
    }

    layer.removeAll();

    // 创建高亮要素
    const graphics: __esri.Graphic[] = [];
    dataToHighlight.forEach((item: any) => {
      if (item.geometry) {
        // 转换GeoJSON几何为ArcGIS几何
        let arcgisGeometry = item.geometry;
        if (
          item.geometry.type &&
          typeof item.geometry.coordinates !== 'undefined'
        ) {
          // 这是GeoJSON格式，需要转换
          arcgisGeometry = convertGeoJSONToArcGIS(item.geometry);
        }

        if (arcgisGeometry) {
          const graphic = new Graphic({
            geometry: arcgisGeometry,
            symbol: setSymbol(arcgisGeometry.type, symbolOptions),
            attributes: item
          });
          graphics.push(graphic);
        }
      }
    });

    if (graphics.length > 0) {
      layer.addMany(graphics);
      console.log(`成功高亮显示${graphics.length}个要素`);

      // 单个要素需要缩放
      if (shouldZoom && graphics.length === 1) {
        await gotoAndHighLight(props.view, graphics[0], {
          avoidHighlight: false,
          zoom: 18
        });
      }
    }
  } catch (error) {
    console.error('高亮要素时出错:', error);
  }
};

// 保留原有方法名作为便捷调用
const highlightCurrentTabFeatures = () => highlightFeatures('all');
const highlightFilteredFeatures = (fieldName: string, fieldValue: string) =>
  highlightFeatures('filter', { fieldName, fieldValue });
const highlightSingleFeature = (objectId: number) =>
  highlightFeatures('single', { objectId });

/**
 * 处理维度表格行点击事件
 */
const handleDimensionTableRowClick = (row: any) => {
  // 切换维度
  handleDimensionClick(row.fieldName);

  // 智能展开逻辑：
  // 1. 如果点击的是当前选中的维度，则切换展开/收起状态
  // 2. 如果点击的是其他维度，则展开该维度（可选择是否收起其他维度）
  const isCurrentlyExpanded = state.expandedRows.includes(row.fieldName);
  const isSelectedDimension = row.fieldName === state.selectedDimension;

  if (isSelectedDimension && isCurrentlyExpanded) {
    // 如果是选中的维度且已展开，则收起
    state.expandedRows = state.expandedRows.filter(
      (fieldName) => fieldName !== row.fieldName
    );
  } else if (!isCurrentlyExpanded) {
    // 如果未展开，则展开该维度
    // 选择1：只展开当前维度，收起其他维度（单一展开模式）
    state.expandedRows = [row.fieldName];

    // 选择2：同时展开多个维度（多重展开模式）
    // state.expandedRows = [...state.expandedRows, row.fieldName]
  }
};

/**
 * 处理表格展开变化（保留此方法以防其他地方调用）
 */
const handleExpandChange = (row: any, expandedRows: any[]) => {
  // 这个方法现在主要用于同步状态，实际展开逻辑在行点击中处理
  state.expandedRows = expandedRows.map((r) => r.fieldName);
};

/**
 * 处理详情项点击
 */
const handleDetailItemClick = async (fieldName: string, item: any) => {
  try {
    // 1. 设置筛选状态
    isFiltering = true;
    // 2. 高亮地图要素
    await highlightFeatures('filter', {
      fieldName: fieldName,
      fieldValue: item.name
    });

    // 3. 筛选详情数据列表
    let currentTab = refTab.value?.dataForm?.type;
    console.log('当前Tab值:', currentTab, 'refTab.value:', refTab.value);
    console.log('记录的用户选择Tab:', currentSelectedTab);

    // 如果currentTab为空，优先使用用户选择的Tab，其次使用第一个tab
    if (!currentTab) {
      if (currentSelectedTab) {
        currentTab = currentSelectedTab;
        console.log('currentTab为空，使用用户选择的Tab:', currentTab);
      } else if (props.tabs && props.tabs.length > 0) {
        currentTab = props.tabs[0].name;
        console.log('currentTab为空，使用默认值:', currentTab);
      }

      // 同时更新refTab的值
      if (currentTab && refTab.value?.dataForm) {
        refTab.value.dataForm.type = currentTab;
        console.log('更新refTab.value.dataForm.type为:', currentTab);
      }
    }

    if (currentTab) {
      // 筛选符合条件的数据
      const filteredData =
        staticState.currentTabData?.filter((dataItem: any) => {
          return dataItem[fieldName] === item.name;
        }) || [];

      // 4. 切换到详情Tab
      state.curTab = '1';

      // 5. 等待下一个tick，确保Tab切换完成
      await nextTick();

      await refreshTable(currentTab, null, true, true, filteredData);

      // 7. 延迟重置筛选状态，避免立即触发其他事件
      setTimeout(() => {
        isFiltering = false;
      }, 100);

      // 发送筛选事件
      emit(
        'attr-row-click',
        {
          fieldName,
          fieldValue: item.name,
          count: filteredData.length
        },
        currentTab
      );
    } else {
      console.error('无法获取currentTab，筛选失败');
      console.log('refTab.value:', refTab.value);
      console.log('props.tabs:', props.tabs);
    }
  } catch (error) {
    console.error('处理详情项点击时出错:', error);
    // 出错时也要重置筛选状态
    isFiltering = false;
  }
};

/**
 * 处理维度切换
 */
const handleDimensionClick = (fieldName: string) => {
  if (state.selectedDimension === fieldName) {
    return;
  }

  state.selectedDimension = fieldName;

  // 更新图表
  updateChartsForDimension(fieldName);

  // 刷新图表
  nextTick(() => {
    refreshChar();
  });
};
const refreshAllDeviceStatistics = async () => {
  let currentTab = refTab.value?.dataForm?.type;

  // 如果currentTab为空，优先使用用户选择的Tab，其次使用第一个tab
  if (!currentTab) {
    if (currentSelectedTab) {
      currentTab = currentSelectedTab;
      console.log('currentTab为空，使用用户选择的Tab:', currentTab);
    } else if (props.tabs && props.tabs.length > 0) {
      currentTab = props.tabs[0].name;
      console.log('currentTab为空，使用默认值:', currentTab);
    }

    // 同时更新refTab的值
    if (currentTab && refTab.value?.dataForm) {
      refTab.value.dataForm.type = currentTab;
      console.log('更新refTab.value.dataForm.type为:', currentTab);
    }
  }

  const currentTabData = props.tabs?.find((item) => item.name === currentTab);

  if (
    !currentTabData ||
    !currentTabData.data ||
    currentTabData.data.length === 0
  ) {
    // 如果没有当前Tab数据，显示空状态
    state.attributes = [];
    state.barOption = null;
    state.ringOption = null;
    return;
  }

  // 获取原始要素数据（去重后的真实数量）
  const uniqueFeatures = new Map();
  currentTabData.data.forEach((item: any) => {
    const objectId = item.OBJECTID;
    if (!uniqueFeatures.has(objectId)) {
      uniqueFeatures.set(objectId, item);
    }
  });

  const realFeatures = Array.from(uniqueFeatures.values());
  // 分别统计各个字段
  const fieldStats = new Map<string, Map<string, number>>();

  // 根据当前Tab确定要统计的字段
  let fieldsToStats: string[] = [];
  if (currentTab === '给水管线' || currentTab === '管线') {
    fieldsToStats = ['管材', '管径'];
  } else if (currentTab === '节点' || currentTab === '测点') {
    fieldsToStats = ['备注'];
  } else {
    // 尝试从数据中自动检测统计字段
    if (realFeatures.length > 0) {
      const sampleItem = realFeatures[0];
      console.log('数据样本:', sampleItem);
      const possibleFields = [
        '管材',
        '材质',
        '管径',
        '备注',
        'SUBTYPE',
        'STATUS'
      ];
      fieldsToStats = possibleFields.filter((field) =>
        sampleItem.hasOwnProperty(field)
      );
      console.log('自动检测到的字段:', fieldsToStats);
    }
  }

  // 根据单位判断统计类型
  const isLengthStatistic = props.unit === 'm' || props.unit === '米';

  // 为每个字段进行统计
  fieldsToStats.forEach((fieldName) => {
    const fieldMap = new Map<string, number>();

    realFeatures.forEach((item: any) => {
      const fieldValue = item[fieldName] || '未知';
      
      // 根据单位判断统计方式
      let value = 1; // 默认数量统计
      if (isLengthStatistic) {
        // 管长统计：使用长度值
        value = Number(item.PIPELENGTH || item.统计值 || item.管长 || item.LENGTH || 0);
      }
      
      fieldMap.set(fieldValue, (fieldMap.get(fieldValue) || 0) + value);
    });

    if (fieldMap.size > 0) {
      fieldStats.set(fieldName, fieldMap);
    }
  });

  // 保存字段统计数据到state，供点击切换使用
  state.fieldStats = fieldStats;
  
  // 计算总计（根据统计类型）
  let totalCount = 0;
  if (isLengthStatistic) {
    // 管长统计：累加所有长度值
    totalCount = realFeatures.reduce((sum, item) => {
      return sum + Number(item.PIPELENGTH || item.管长 || item.LENGTH || 0);
    }, 0);
    totalCount = totalCount.toFixed(1); 
  } else {
    // 数量统计：要素数量
    totalCount = realFeatures.length;
  }
  state.totalCount = totalCount;

  // 保存当前Tab的数据供地图高亮使用
  staticState.currentTabData = realFeatures;

  // 构建新的表格数据结构
  state.dimensionTableData = Array.from(fieldStats.entries()).map(
    ([fieldName, valueMap]) => {
      const total = Array.from(valueMap.values()).reduce(
        (sum, count) => sum + count,
        0
      );
      const details = Array.from(valueMap.entries())
        .sort((a, b) => b[1] - a[1]) // 按统计值降序排列
        .map(([name, count]) => ({
          name,
          count:isLengthStatistic ? count.toFixed(1) : count,
          percentage: totalCount > 0 ? Number(((count / totalCount) * 100).toFixed(1)) : 0
        }));
      return {
        fieldName,
        total:isLengthStatistic ? total.toFixed(1) : total,
        types: valueMap.size,
        details
      };
    }
  );

  // 重置或设置选中的维度
  const availableFields = Array.from(fieldStats.keys());

  // 如果当前选中的维度在新的字段列表中不存在，或者没有选中维度，则选择第一个
  if (
    !state.selectedDimension ||
    !availableFields.includes(state.selectedDimension)
  ) {
    if (availableFields.length > 0) {
      state.selectedDimension = availableFields[0];
    } else {
      state.selectedDimension = '';
    }
  }

  // 更新展开状态 - 默认展开选中的维度
  if (state.selectedDimension) {
    state.expandedRows = [state.selectedDimension];
  } else {
    state.expandedRows = [];
  }

  // 根据选中的维度构建图表数据
  if (state.selectedDimension) {
    updateChartsForDimension(state.selectedDimension);
  } else {
    // 如果没有可用字段，清空图表
    state.barOption = null;
    state.ringOption = null;
  }

  // 高亮当前Tab的所有要素
  highlightCurrentTabFeatures();

  // 刷新图表
  await nextTick();
  refreshChar();
};
const refreshStatistics = async () => {
  debugger
  // 如果没有tabs数据，直接返回
  if (!props.tabs || props.tabs.length === 0) {
    console.log('没有tabs数据');
    return;
  }
  
  // 获取当前活动的tab数据
  const currentTab = props.tabs.find(tab => tab.name === refTab.value?.dataForm?.type) || props.tabs[0];
  if (!currentTab || !currentTab.data || currentTab.data.length === 0) {
    console.log('当前tab没有数据');
    return;
  }
  
  // 模拟原有的接口返回数据结构，使用前端逻辑计算
  const rawData = currentTab.data;
  const groupFields = props.statisticsParams.group_fields || [];
  const statisticField = props.statisticsParams.statistic_field;
  const statisticType = props.statisticsParams.statistic_type;
  const isLengthStatistic = statisticType === '2' || props.unit === 'm';
  
  // 按分组字段进行统计，模拟接口返回的数据格式
  const statisticsMap = new Map<string, number>();
  
  rawData.forEach((item: any) => {
    // 获取分组键（优先使用第一个分组字段）
    let groupKey = '未分类';
    for (const field of groupFields) {
      if (item[field] && item[field] !== '' && item[field] !== null) {
        groupKey = String(item[field]);
        break;
      }
    }
    
    // 根据统计类型计算值
    let value = 1; // 默认数量统计
    if (isLengthStatistic) {
      value = Number(item['管长'] || 0);
    }
    
    // 累加到分组统计
    const currentValue = statisticsMap.get(groupKey) || 0;
    statisticsMap.set(groupKey, currentValue + value);
  });
  
  // 将统计结果转换为原有接口格式的数据
  const data = Array.from(statisticsMap.entries()).map(([groupKey, value]) => {
    const result: any = {};
    result[props.statisticsParams.group_fields[0]] = groupKey;
    result[props.statisticsParams.statistic_field] = value;
    return result;
  });
  
  // 保持原有的数据处理逻辑
  const barData =
    data.map((item) => {
      return {
        value: item[props.statisticsParams.statistic_field],
        key:
          (props.prefix || '') +
          (item[props.statisticsParams.group_fields[0]]?.toString() || '--')
      };
    }) || [];
  
  state.attributes =
    data.map((item) => {
      return {
        value:
          (item[props.statisticsParams.statistic_field] || '--') +
          (props.unit || ''),
        label:
          (props.prefix || '') +
          (item[props.statisticsParams.group_fields[0]]?.toString() || '--')
      };
    }) || [];
  debugger
  state.barOption = zhuzhuangtu(barData, props.unit);
  
  const ringData =
    data.map((item) => {
      const transValue = transNumberUnit(
        item[props.statisticsParams.statistic_field] || 0
      );
      return {
        value: item[props.statisticsParams.statistic_field] || 0,
        name:
          (props.prefix || '') +
          (item[props.statisticsParams.group_fields[0]] || '--'),
        valueAlias:
          transValue.value.toFixed(props.percision || 2) + transValue.unit,
        scale: '0%'
      };
    }) || [];
  
  const total = ringData.reduce((prev, cur) => {
    return cur.value + prev;
  }, 0);
  
  ringData.map((item) => {
    item.scale =
      total === 0
        ? '0%'
        : ((item.value / total) * 100).toFixed(props.percision || 2) + '%';
  });
  
  state.attributes.push({
    label: '合计',
    notLink: true,
    value:
      total.toFixed(props.statisticsParams.statistic_type === '1' ? 0 : 2) +
      (props.unit || '')
  });
  
  state.ringOption = ring(
    ringData,
    props.unit,
    props.prefix,
    props.percision || 2
  );

  refreshChar();
};

const refreshTable = async (
  tab: any,
  layerId: any = null,
  showAll?: boolean,
  custom?: boolean,
  allOids?: any[]
) => {
  emit('detail-refreshing');
  try {
    TableConfig.loading = true;
    if (!tab) {
      console.log('没有tab，清空表格数据');
      TableConfig.dataList = [];
      TableConfig.loading = false;
      return;
    }

    // 获取要显示的数据源
    const allData = custom
      ? allOids
      : props.tabs?.find((item) => item.name === tab)?.data;

    if (!allData || !Array.isArray(allData) || allData.length === 0) {
      TableConfig.dataList = [];
      TableConfig.pagination.total = 0;
      TableConfig.loading = false;
      return;
    }

    // 设置总数
    TableConfig.pagination.total = allData.length;

    // 计算当前页数据
    const currentPage = TableConfig.pagination.page || 1;
    const pageSize = TableConfig.pagination.limit || 20;
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const currentPageData = allData.slice(startIndex, endIndex);

    // 构建表格列配置（基于数据的属性）
    const columns: IFormTableColumn[] = [];
    if (allData.length > 0) {
      const sampleData = allData[0];
      Object.keys(sampleData).forEach((key) => {
        if (skipedFields.indexOf(key) === -1) {
          const conf: IFormTableColumn = {
            label: key,
            prop: key,
            minWidth: 160
          };
          // 如果是日期字段，添加格式化
          if (
            key.toLowerCase().includes('date') ||
            key.toLowerCase().includes('time')
          ) {
            conf.formatter = (row: any) => {
              const value = row[key];
              if (value && typeof value === 'number') {
                return formatDate(value, formatterDate);
              }
              return value || '--';
            };
          }
          columns.push(conf);
        }
      });
    }
    TableConfig.columns = columns;

    // 设置当前页数据
    TableConfig.dataList = currentPageData;

    // 处理地图显示（简化版本）
    const layer = getGraphicLayer(props.view, {
      id: 'pipe-detail',
      title: '详情'
    });

    if (!allData?.length) {
      layer?.removeAll();
    } else if (showAll) {
      // 对于统计模式，主要关注数据展示，地图显示可以后续优化
      console.log('统计模式下，地图显示功能暂时简化');
      if (layer) {
        layer.removeAll();
      }
      // 保存数据供其他功能使用
      staticState.tabFeatures = allData;
    }
  } catch (error) {
    console.dir(error);
    SLMessage.error('查询失败');
  }
  TableConfig.loading = false;
  emit('detail-refreshed');
};
const initTab = () => {
  console.log('initTab被调用，isFiltering:', isFiltering);

  // 如果正在筛选状态，不要重新初始化Tab
  if (isFiltering) {
    console.log('正在筛选状态，跳过initTab');
    return;
  }

  const tabField = FormConfig.group[0].fields[0] as ITabs;
  if (!tabField) return;
  const tabs = props.tabs || [];
  tabField.tabs =
    tabs.map((item) => {
      return {
        ...item,
        value: item.name
      };
    }) || [];
  // 优先使用用户已选择的Tab，如果没有则使用第一个Tab
  let tab = currentSelectedTab;
  if (!tab && tabs.length > 0) {
    tab = tabs[0]?.name;
  }

  console.log('initTab选择的tab:', tab, '用户选择的Tab:', currentSelectedTab);

  // 只有当tab存在时才进行初始化
  if (tab) {
    refTab.value && (refTab.value.dataForm.type = tab);

    // 同步更新Form的Tab选择状态
    const tabField = FormConfig.group[0].fields[0] as ITabs;
    if (tabField && tabField.tabs) {
      // 确保Form显示正确的选中状态
      console.log('同步Form的Tab选择状态为:', tab);
    }

    // 只有在没有用户选择时才记录初始化的Tab
    if (!currentSelectedTab) {
      currentSelectedTab = tab;
      console.log('initTab首次记录选择的Tab:', currentSelectedTab);
    }
    console.log('initTab调用refreshDetail，tab:', tab);
    refreshDetail(tab, true);
  } else {
    console.log('initTab: 没有可用的tab，跳过refreshDetail');
  }
};
const extentTo = async (view: __esri.MapView, tab?: string, oid?: string) => {
  tab = tab || refTab.value?.dataForm?.type;
  const feature = staticState.tabFeatures.find(
    (item) => item.attributes.OBJECTID === oid
  );
  if (feature) {
    await gotoAndHighLight(view, feature);
  }
};
const getCurLayer = () => {
  return refTab.value?.dataForm?.type;
};
const refreshChar = () => {
  if (refBar.value && state.barOption) {
    refBar.value.resize();
  } else {
    console.log(
      '柱状图刷新失败，refBar:',
      refBar.value,
      'barOption:',
      state.barOption
    );
  }

  if (refRing.value && state.ringOption) {
    refRing.value.resize();
  } else {
    console.log(
      '环形图刷新失败，refRing:',
      refRing.value,
      'ringOption:',
      state.ringOption
    );
  }
};

watch(
  () => props.tabs,
  () => {
    console.log('props.tabs变化，触发initTab');
    initTab();
  }
);
// 添加一个状态来跟踪是否正在筛选
let isFiltering = false;

// 添加一个变量来记住当前选择的Tab，避免被重置
let currentSelectedTab: string | null = null;

watch(
  () => state.curTab,
  async (newTab, oldTab) => {
    console.log('Tab切换监听器触发:', { newTab, oldTab, isFiltering });
    await nextTick();
    if (newTab === '0') {
      console.log('切换到统计Tab，刷新图表');
      // 切换到统计Tab时重置筛选状态
      if (isFiltering) {
        console.log('重置筛选状态');
        isFiltering = false;
      }
      refreshChar();
    } else if (newTab === '1' && oldTab === '0') {
      // 从统计Tab切换到详情Tab时，只有在非筛选状态下才刷新表格数据
      console.log('从统计Tab切换到详情Tab，isFiltering:', isFiltering);
      if (!isFiltering) {
        console.log('非筛选状态，执行正常的表格数据刷新');
        let currentTab = refTab.value?.dataForm?.type;
        console.log('Tab切换监听器中的currentTab:', currentTab);

        // 如果currentTab为空，优先使用用户选择的Tab，其次使用第一个tab
        if (!currentTab) {
          if (currentSelectedTab) {
            currentTab = currentSelectedTab;
            console.log(
              'Tab切换监听器：currentTab为空，使用用户选择的Tab:',
              currentTab
            );
          } else if (props.tabs && props.tabs.length > 0) {
            currentTab = props.tabs[0].name;
            console.log(
              'Tab切换监听器：currentTab为空，使用默认值:',
              currentTab
            );
          }

          // 同时更新refTab的值
          if (currentTab && refTab.value?.dataForm) {
            refTab.value.dataForm.type = currentTab;
            console.log(
              'Tab切换监听器：更新refTab.value.dataForm.type为:',
              currentTab
            );
          }
        }

        if (currentTab) {
          refreshTable(currentTab, null, true);
        } else {
          console.error('Tab切换监听器：无法获取currentTab');
        }
      } else {
        console.log('筛选状态，跳过表格数据刷新，保持筛选结果');
      }
    }
  }
);
const detector = useDetector();
onMounted(() => {
  initTab();
  detector.listenToMush(document.documentElement, () => {
    refreshChar();
  });
});
defineExpose({
  refreshChar,
  refreshDetail,
  setCurLayer,
  getCurLayer,
  refreshTable
});
</script>
<style lang="scss" scoped>
.detail-wrapper {
  height: 100%;
  width: 100%;
  .empty {
    height: 100%;
    width: 100%;
    display: grid;
    place-items: center;
  }
}

.statistics-tab-wrapper {
  width: 100%;
  height: 100%;
  .tab-wrapper {
    width: 100%;
    height: 40px;
    padding: 0 8px;
    position: relative;
  }

  .filter-box {
    position: absolute;
    right: 10px;
    top: 15px;
  }
  .content {
    display: flex;
    width: 100%;
    height: calc(100% - 40px);
    padding: 8px;
  }
  .right {
    width: calc(100% - 46px);
    height: 100%;

    .statistics-chart {
      width: 100%;
      height: 100%;
      display: flex;
      gap: 15px;

      .attr-table-container {
        flex-shrink: 0; // 不允许压缩
        min-width: 200px; // 最小宽度
        max-width: 400px; // 最大宽度
        width: auto; // 根据内容自动调整
        height: 100%;

        .attr-table-box {
          width: 100%;
          height: 100%;
          overflow-y: auto;
        }
      }

      .charts-container {
        flex: 1; // 占据剩余空间
        height: 100%;
        display: flex;
        gap: 10px;
        min-width: 550px; // 确保图表容器有足够的最小宽度（300+250）

        .bar-container {
          flex: 0 0 60%; // 柱状图占图表区域的60%
          height: 100%;
          min-width: 300px; // 最小宽度保证基本可用性

          .bar {
            width: 100%;
            height: 100%;
          }
        }

        .ring-container {
          flex: 0 0 40%; // 环形图占图表区域的40%
          height: 100%;
          min-width: 250px; // 最小宽度保证饼图不被压缩

          .ring {
            width: 100%;
            height: 100%;
          }
        }
      }

      // 兼容旧的el-col布局（如果还有使用的地方）
      .el-col {
        height: 100%;

        .bar,
        .ring {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
  .tabs {
    width: 46px;
    height: 100%;
    padding-right: 10px;
    .tab-item {
      cursor: pointer;
      font-size: 14px;
      padding: 10px;
      width: 100%;
      height: auto;
      line-height: 30px;
      min-height: 127px;
      display: grid;
      place-items: center;
      background-color: transparent;
      box-shadow: 0 0 0 1px var(--el-color-primary) inset;
      &.active {
        color: #fff;
        background-color: var(--el-color-primary);
      }
      &:hover {
        color: #fff;
        background-color: var(--el-color-primary);
      }
    }
  }
}
.statistics-table {
  width: 100%;
  height: 100%;
}

// 新的表格样式
.attr-table-container {
  .attr-table-box {
    .el-table {
      border-radius: 8px;
      overflow: hidden;

      .el-table__header {
        background-color: #f8f9fa;

        th {
          background-color: #f8f9fa !important;
          color: #606266;
          font-weight: 600;
          border-bottom: 2px solid #e4e7ed;
        }
      }

      .el-table__body {
        tr {
          cursor: pointer;
          transition: background-color 0.2s;

          &:hover {
            background-color: rgba(64, 158, 255, 0.05) !important;
          }

          td {
            border-bottom: 1px solid #f0f0f0;

            .dimension-name {
              display: flex;
              align-items: center;
              gap: 8px;

              .selected-icon {
                color: #409eff;
                font-size: 16px;
              }
            }

            .total-count,
            .type-count {
              font-family: 'Consolas', 'Monaco', monospace;
              font-weight: 500;
            }

            .total-count {
              color: #409eff;
            }

            .type-count {
              color: #67c23a;
            }
          }
        }
      }

      .el-table__expanded-cell {
        padding: 0 !important;
        background-color: #fafbfc;

        .dimension-detail {
          padding: 16px 20px;

          .detail-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 1px solid #e4e7ed;

            .detail-title {
              font-weight: 600;
              color: #303133;
              font-size: 14px;
            }

            .detail-count {
              color: #909399;
              font-size: 12px;
            }
          }

          .detail-items {
            .detail-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 8px 12px;
              margin-bottom: 4px;
              background-color: white;
              border-radius: 6px;
              border: 1px solid #f0f0f0;
              cursor: pointer;
              transition: all 0.2s;

              &:hover {
                border-color: #409eff;
                box-shadow: 0 2px 4px rgba(64, 158, 255, 0.1);
                transform: translateY(-1px);
              }

              .item-info {
                display: flex;
                align-items: center;
                gap: 12px;

                .item-name {
                  font-weight: 500;
                  color: #303133;
                }

                .item-count {
                  color: #409eff;
                  font-family: 'Consolas', 'Monaco', monospace;
                  font-size: 13px;
                }
              }

              .item-progress {
                display: flex;
                align-items: center;
                gap: 8px;

                .progress-bar {
                  width: 60px;
                  height: 6px;
                  background-color: #f0f0f0;
                  border-radius: 3px;
                  overflow: hidden;

                  .progress-fill {
                    height: 100%;
                    background: linear-gradient(90deg, #409eff, #67c23a);
                    border-radius: 3px;
                    transition: width 0.3s ease;
                  }
                }

                .percentage {
                  font-size: 12px;
                  color: #909399;
                  font-family: 'Consolas', 'Monaco', monospace;
                  min-width: 35px;
                  text-align: right;
                }
              }
            }
          }
        }
      }
    }

    .table-summary {
      margin-top: 12px;
      padding: 12px 16px;
      background-color: #f8f9fa;
      border-radius: 6px;
      border: 1px solid #e4e7ed;

      .summary-item {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .summary-label {
          color: #606266;
          font-weight: 500;
        }

        .summary-value {
          color: #409eff;
          font-weight: 600;
          font-family: 'Consolas', 'Monaco', monospace;
        }
      }
    }
  }
}

// 图表容器响应式调整
@media (max-width: 1600px) {
  .statistics-chart {
    .charts-container {
      .bar-container {
        flex: 0 0 65% !important; // 大屏幕时柱状图占65%
        min-width: 280px !important;
      }
      .ring-container {
        flex: 0 0 35% !important; // 大屏幕时环形图占35%
        min-width: 220px !important;
      }
    }
  }
}

@media (max-width: 1200px) {
  .statistics-chart {
    .charts-container {
      .bar-container {
        flex: 0 0 70% !important; // 中屏幕时柱状图占70%
        min-width: 250px !important;
      }
      .ring-container {
        flex: 0 0 30% !important; // 中屏幕时环形图占30%
        min-width: 200px !important;
      }
    }
  }
}

@media (max-width: 900px) {
  .statistics-chart {
    flex-direction: column !important;

    .attr-table-container {
      max-width: none !important;
      height: auto !important;
      flex: 0 0 auto;
      max-height: 300px; // 限制属性表在小屏幕时的高度
    }

    .charts-container {
      flex-direction: column !important;
      gap: 15px !important;

      .bar-container,
      .ring-container {
        flex: 1 !important;
        min-height: 250px !important;
        min-width: auto !important; // 移除最小宽度限制
      }
    }
  }
}

@media (max-width: 600px) {
  .statistics-chart {
    .attr-table-container {
      max-height: 200px !important; // 超小屏幕时进一步限制属性表高度
    }

    .charts-container {
      .bar-container,
      .ring-container {
        min-height: 200px !important; // 超小屏幕时减小图表高度
      }
    }
  }
}
</style>
