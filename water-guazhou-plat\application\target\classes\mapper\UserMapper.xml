<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.user.UserMapper">

    <select id="findByDepartmentIdIn" resultType="org.thingsboard.server.dao.model.sql.UserEntity">
        select distinct u.id as tempId, u.additional_info, u.authority, u.customer_id, u.email, u.first_name,
        u.last_name, u.search_text, u.tenant_id, u.phone, u.login_name, u.serial_no, u.department_id, u.create_time ,
        d.name as departmentName, d.id as departmentId, o.name as organizationName, uc.enabled as status,
        string_agg(cr.name ,',') roleName
        from tb_user u
        left join tb_department d on u.department_id = d.id
        left join tb_organization o on d.parent_id = o.id
        left join customer_user_role ur on u.id = ur.user_id
        left join user_credentials uc on u.id = uc.user_id
        left join customer_role cr on ur.role_id = cr.id
        where
        d.id in
        <foreach collection="departmentIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and u.first_name like '%'||#{name}||'%'
        and u.tenant_id = #{tenantId}

        <choose>
            <when test="roleId == null or roleId == ''.toString()">
                and (ur.role_id like '%'||#{roleId}||'%' or ur.role_id is null)
            </when>
            <otherwise>
                and ur.role_id like '%'||#{roleId}||'%'
            </otherwise>
        </choose>
        <if test="status != null">
            and uc.enabled = #{status}
        </if>

        group by u.id , u.additional_info, u.authority, u.customer_id, u.email, u.first_name, u.last_name,
        u.search_text, u.tenant_id, u.phone, u.login_name, uc.enabled, u.serial_no, u.department_id, u.create_time ,
        d.name , d.id, o.name

        order by d.id,u.create_time
        limit #{size} offset (#{page} - 1) * #{size}
    </select>

    <select id="findByDeptIdIn" resultType="org.thingsboard.server.dao.model.sql.UserEntity">
        select distinct u.id as tempId, u.additional_info, u.authority, u.customer_id, u.email, u.first_name,
        u.last_name, u.search_text, u.tenant_id, u.phone, u.login_name, u.serial_no, u.department_id, u.create_time ,
        d.name as departmentName, d.id as departmentId, o.name as organizationName, uc.enabled as status,
        string_agg(cr.name ,',') roleName
        from tb_user u
        left join tb_department d on u.department_id = d.id
        left join tb_organization o on d.parent_id = o.id
        left join customer_user_role ur on u.id = ur.user_id
        left join user_credentials uc on u.id = uc.user_id
        left join customer_role cr on ur.role_id = cr.id
        where
        d.id in
        <foreach collection="departmentIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        group by u.id , u.additional_info, u.authority, u.customer_id, u.email, u.first_name, u.last_name,
        u.search_text, u.tenant_id, u.phone, u.login_name, uc.enabled, u.serial_no, u.department_id, u.create_time ,
        d.name , d.id, o.name

        order by d.id,u.create_time
    </select>

    <select id="findByDepartmentIdInCount" resultType="int">
        select count(distinct u.id)
        from tb_user u
        left join tb_department d on u.department_id = d.id
        left join tb_organization o on d.parent_id = o.id
        left join customer_user_role ur on u.id = ur.user_id
        left join user_credentials uc on u.id = uc.user_id
        where d.id in
        <foreach collection="departmentIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and u.first_name like '%'||#{name}||'%'
        and u.tenant_id = #{tenantId}
        <choose>
            <when test="roleId == null or roleId == ''.toString()">
                and (ur.role_id like '%'||#{roleId}||'%' or ur.role_id is null)
            </when>
            <otherwise>
                and ur.role_id like '%'||#{roleId}||'%'
            </otherwise>
        </choose>
        <if test="status != null">
            and uc.enabled = #{status}
        </if>
    </select>

    <select id="getNameByMultiId" resultType="java.lang.String">
        select user_resolve_multi_id(#{multiId})
    </select>

    <select id="findByPidStr" resultType="org.thingsboard.server.dao.model.sql.UserEntity">
        select distinct u.id as tempId, u.additional_info, u.authority, u.customer_id, u.email, u.first_name,
        u.last_name, u.search_text, u.tenant_id, u.phone, u.login_name, u.create_time
        from tb_user u
        left join tb_department d on u.department_id = d.id
        left join user_credentials uc on u.id = uc.user_id
        where
        d.id in
        <foreach collection="pid.split(',')" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and uc.enabled = true
        and u.tenant_id = #{tenantId}
        order by u.create_time desc
    </select>

    <select id="existsById" resultType="boolean">
        select count(1)
        from tb_user
        where id = #{id}
    </select>

    <select id="findByDepartmentId" resultType="org.thingsboard.server.dao.model.sql.UserEntity">
        select id,
               additional_info,
               authority,
               customer_id,
               email,
               first_name,
               last_name,
               search_text,
               tenant_id,
               phone,
               login_name,
               serial_no,
               department_id,
               create_time
        from tb_user
        where department_id = #{departmentId}
    </select>
    <select id="selectAll" resultType="org.thingsboard.server.dao.model.sql.UserEntity">
        select distinct u.id   as tempId,
                        u.additional_info,
                        u.authority,
                        u.customer_id,
                        u.email,
                        u.first_name,
                        u.last_name,
                        u.search_text,
                        u.tenant_id,
                        u.phone,
                        u.login_name,
                        u.serial_no,
                        u.department_id,
                        u.create_time,
                        d.name as departmentName,
                        o.name as organizationName
        from tb_user u
                 left join tb_department d on u.department_id = d.id
                 left join tb_organization o on d.parent_id = o.id
        where u.first_name like '%' || #{name} || '%'
          and u.tenant_id = #{tenantId}
        order by first_name
    </select>

    <select id="findByUserId" resultType="org.thingsboard.server.dao.model.sql.UserEntity">
        select distinct u.id   as tempId,
                        u.additional_info,
                        u.authority,
                        u.customer_id,
                        u.email,
                        u.first_name,
                        u.last_name,
                        u.search_text,
                        u.tenant_id,
                        u.phone,
                        u.login_name,
                        u.serial_no,
                        u.department_id,
                        u.create_time,
                        u.control_key,
                        d.name as departmentName,
                        o.name as organizationName
        from tb_user u
                 left join tb_department d on u.department_id = d.id
                 left join tb_organization o on d.parent_id = o.id
        where u.id = #{id}
    </select>

    <select id="findUserByGisUserTypeAndDepartmentId" resultType="org.thingsboard.server.dao.model.sql.UserEntity">
        select u.id                                                as tempId,
               u.additional_info,
               u.authority,
               u.customer_id,
               u.email,
               u.first_name,
               u.last_name,
               u.search_text,
               u.tenant_id,
               u.phone,
               u.login_name,
               u.serial_no,
               u.department_id,
               u.create_time,
               department_get_name(u.department_id)                as departmentName,
               department_get_direct_organization(u.department_id) as organizationName
        from tb_user u
                 left join (select distinct user_id
                            from customer_user_role
                                     join (select regexp_split_to_table(roles, ',')
                                           from tb_gis_people_setting
                                           where type = 'XUNJIANRENYUAN') xj_role(xj_role_id)
                                          on customer_user_role.role_id = xj_role.xj_role_id) xj_user(user_id)
                           on u.id = xj_user.user_id
        where department_id = #{departmentId}
          and tenant_id = #{tenantId}
    </select>

    <!-- 根据用户ID查询控制密钥 -->
    <select id="findControlKeyByUserId" resultType="java.lang.String">
        SELECT control_key
        FROM tb_user
        WHERE id = #{userId}
    </select>

    <!-- 查询所有已存在的控制密钥 -->
    <select id="findAllControlKeys" resultType="java.lang.String">
        SELECT control_key
        FROM tb_user
        WHERE control_key IS NOT NULL AND control_key != ''
    </select>
</mapper>