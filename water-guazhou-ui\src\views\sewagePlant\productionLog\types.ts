// 污水处理厂生产日志相关类型定义

// 生产日志数据
export interface ProductionLogData {
  id?: string
  stationId: string // 站点ID
  stationName: string // 采样点位
  logDate: string // 采样时间
  weather: string // 水质类别
  codOutlet: number // COD mg/L
  ammoniaNitrogenOutlet: number // 氨氮 mg/L
  totalNitrogenOutlet: number // 总氮 mg/L
  totalPhosphorusOutlet: number // 总磷 mg/L
  phOutlet: number // 出水PH值
  flowRate: number // 流量 m³/h
  createTime?: string
  updateTime?: string
}

// 查询表单数据
export interface QueryForm {
  pageNum: number
  pageSize: number
  stationId?: string
  stationName?: string
  dateRange?: string[] | null
  operatorName?: string
}

// 污水处理厂站点信息
export interface SewagePlantStation {
  id: string
  stationId: string
  name: string
  stationName?: string
  location?: string
  status?: number
  projectId?: string
  projectName?: string
  createTime?: string
  updateTime?: string
}

// 生产日志统计数据
export interface ProductionLogStatistics {
  totalLogs: number // 总日志数
  avgFlowRate: number // 平均流量
  avgPowerConsumption: number // 平均耗电量
  avgChemicalConsumption: number // 平均药剂消耗
  avgSludgeProduction: number // 平均污泥产量
  complianceRate: number // 达标率
  lastUpdateTime: string // 最后更新时间
}

// API响应数据结构
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  success?: boolean
}

// 分页响应数据
export interface PageResponse<T = any> {
  list: T[]
  total: number
  pageNum: number
  pageSize: number
}

// 表单验证规则
export interface FormRules {
  [key: string]: Array<{
    required?: boolean
    message: string
    trigger?: string
    type?: string
    min?: number
    max?: number
    validator?: (rule: any, value: any, callback: any) => void
  }>
}

// 导出参数
export interface ExportParams {
  stationId?: string
  dateRange?: string[]
  operatorName?: string
  format?: 'excel' | 'pdf'
}

// 表格列配置
export interface TableColumn {
  prop: string
  label: string
  width?: number
  minWidth?: number
  fixed?: boolean | string
  sortable?: boolean
  formatter?: (row: any, column: any, cellValue: any, index: number) => string
}
