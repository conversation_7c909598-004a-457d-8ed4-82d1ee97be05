// 污水处理厂生产日志相关类型定义

// 生产日志数据
export interface ProductionLogData {
  id?: string
  stationId: string // 站点ID
  stationName: string // 站点名称
  logDate: string // 日志日期
  weather: string // 天气
  codInlet: number // 进水COD mg/L
  bodInlet: number // 进水BOD5 mg/L
  suspendedSolidsInlet: number // 进水悬浮物 mg/L
  ammoniaNitrogenInlet: number // 进水氨氮 mg/L
  totalNitrogenInlet: number // 进水总氮 mg/L
  totalPhosphorusInlet: number // 进水总磷 mg/L
  phInlet: number // 进水PH值
  codOutlet: number // 出水COD mg/L
  bodOutlet: number // 出水BOD5 mg/L
  suspendedSolidsOutlet: number // 出水悬浮物 mg/L
  ammoniaNitrogenOutlet: number // 出水氨氮 mg/L
  totalNitrogenOutlet: number // 出水总氮 mg/L
  totalPhosphorusOutlet: number // 出水总磷 mg/L
  phOutlet: number // 出水PH值
  flowRate: number // 流量 m³/h
  powerConsumption: number // 耗电量 kWh
  chemicalConsumption: number // 药剂消耗量 kg
  sludgeProduction: number // 污泥产量 t
  operatorName: string // 操作员姓名
  remarks?: string // 备注
  createTime?: string
  updateTime?: string
}

// 查询表单数据
export interface QueryForm {
  pageNum: number
  pageSize: number
  stationId?: string
  stationName?: string
  dateRange?: string[] | null
  operatorName?: string
}

// 污水处理厂站点信息
export interface SewagePlantStation {
  id: string
  stationId: string
  name: string
  stationName?: string
  location?: string
  status?: number
  projectId?: string
  projectName?: string
  createTime?: string
  updateTime?: string
}

// 生产日志统计数据
export interface ProductionLogStatistics {
  totalLogs: number // 总日志数
  avgFlowRate: number // 平均流量
  avgPowerConsumption: number // 平均耗电量
  avgChemicalConsumption: number // 平均药剂消耗
  avgSludgeProduction: number // 平均污泥产量
  complianceRate: number // 达标率
  lastUpdateTime: string // 最后更新时间
}

// API响应数据结构
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  success?: boolean
}

// 分页响应数据
export interface PageResponse<T = any> {
  list: T[]
  total: number
  pageNum: number
  pageSize: number
}

// 表单验证规则
export interface FormRules {
  [key: string]: Array<{
    required?: boolean
    message: string
    trigger?: string
    type?: string
    min?: number
    max?: number
    validator?: (rule: any, value: any, callback: any) => void
  }>
}

// 导出参数
export interface ExportParams {
  stationId?: string
  dateRange?: string[]
  operatorName?: string
  format?: 'excel' | 'pdf'
}

// 表格列配置
export interface TableColumn {
  prop: string
  label: string
  width?: number
  minWidth?: number
  fixed?: boolean | string
  sortable?: boolean
  formatter?: (row: any, column: any, cellValue: any, index: number) => string
}
