package org.thingsboard.server.dao.sql.user;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.dao.model.sql.UserEntity;

import java.util.List;

/**
 * userMybatis
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-23
 */
@Mapper
public interface UserMapper extends BaseMapper<UserEntity> {
    List<UserEntity> findByDepartmentIdIn(@Param("departmentIdList") List<String> departmentIdList, @Param("name") String name, @Param("roleId") String roleId, @Param("status") Boolean status, @Param("page") int page, @Param("size") int size, @Param("tenantId") String tenantId);

    int findByDepartmentIdInCount(@Param("departmentIdList") List<String> departmentIdList, @Param("name") String name, @Param("roleId") String roleId, @Param("status") Boolean status, @Param("tenantId") String tenantId);

    List<UserEntity> findByPidStr(@Param("pid") String pid, @Param("tenantId") String tenantId);

    String getNameByMultiId(String multiId);

    boolean existsById(String userId);

    List<UserEntity> findByDepartmentId(String departmentId);

    List<UserEntity> selectAll(@Param("name") String name, @Param("tenantId") String tenantId);

    UserEntity findByUserId(@Param("id") String id);

    List<UserEntity> findUserByGisUserTypeAndDepartmentId(@Param("departmentId") String departmentId,
                                                          @Param("type") DataConstants.PEOPLE_TYPE type,
                                                          @Param("tenantId") String tenantId);

    List<UserEntity> findByDeptIdIn(List<String> deptId);

    /**
     * 根据用户ID查询用户的控制密钥
     * @param userId 用户ID
     * @return 控制密钥
     */
    String findControlKeyByUserId(@Param("userId") String userId);

    /**
     * 查询所有已存在的控制密钥
     * @return 所有控制密钥列表
     */
    List<String> findAllControlKeys();
}
