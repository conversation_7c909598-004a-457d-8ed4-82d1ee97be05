import request from '@/plugins/axios'
import type { 
  ProductionLogData, 
  QueryForm, 
  SewagePlantStation,
  ProductionLogStatistics,
  ApiResponse,
  PageResponse,
  ExportParams
} from './types'

/**
 * 获取污水处理厂站点列表
 * @param params 查询参数
 * @returns 
 */
export function getSewagePlantStationList(params?: { projectId?: string }) {
  return request<ApiResponse<SewagePlantStation[]>>({
    url: '/api/mapservice/onemap/getWaterPlantSupply',
    method: 'get',
    params
  })
}

/**
 * 获取生产日志列表
 * @param params 查询参数
 * @returns
 */
export function getProductionLogList(params: QueryForm) {
  const { dateRange, ...otherParams } = params
  const requestParams = {
    ...otherParams,
    startDate: dateRange && dateRange[0] ? dateRange[0] : undefined,
    endDate: dateRange && dateRange[1] ? dateRange[1] : undefined
  }

  // 暂时返回模拟数据，实际项目中应该调用真实API
  return new Promise<ApiResponse<PageResponse<ProductionLogData>>>((resolve) => {
    setTimeout(() => {
      const mockData: ProductionLogData[] = [
        {
          id: '1',
          stationId: 'yyyy-HM-dd-hhmmmss',
          stationName: '污水处理厂A',
          logDate: '2025-04-10',
          weather: '晴',
          codInlet: 43,
          bodInlet: 5,
          suspendedSolidsInlet: 5,
          ammoniaNitrogenInlet: 5,
          totalNitrogenInlet: 7,
          totalPhosphorusInlet: 3,
          phInlet: 7.2,
          codOutlet: 15,
          bodOutlet: 3,
          suspendedSolidsOutlet: 3,
          ammoniaNitrogenOutlet: 2,
          totalNitrogenOutlet: 4,
          totalPhosphorusOutlet: 1,
          phOutlet: 7.0,
          flowRate: 1200,
          powerConsumption: 850,
          chemicalConsumption: 45,
          sludgeProduction: 12,
          operatorName: '张三',
          remarks: '正常运行',
          createTime: '2025-04-10 09:00:00'
        },
        {
          id: '2',
          stationId: 'yyyy-HM-dd-hhmmmss',
          stationName: '污水处理厂A',
          logDate: '2025-04-12',
          weather: '阴',
          codInlet: 40,
          bodInlet: 4,
          suspendedSolidsInlet: 4,
          ammoniaNitrogenInlet: 4,
          totalNitrogenInlet: 8,
          totalPhosphorusInlet: 7,
          phInlet: 7.1,
          codOutlet: 14,
          bodOutlet: 2.5,
          suspendedSolidsOutlet: 2.8,
          ammoniaNitrogenOutlet: 1.8,
          totalNitrogenOutlet: 3.5,
          totalPhosphorusOutlet: 0.9,
          phOutlet: 6.9,
          flowRate: 1150,
          powerConsumption: 820,
          chemicalConsumption: 42,
          sludgeProduction: 11,
          operatorName: '李四',
          remarks: '设备维护',
          createTime: '2025-04-12 09:00:00'
        },
        {
          id: '3',
          stationId: 'yyyy-HM-dd-hhmmmss',
          stationName: '污水处理厂B',
          logDate: '2025-04-20',
          weather: '雨',
          codInlet: 50,
          bodInlet: 5,
          suspendedSolidsInlet: 5,
          ammoniaNitrogenInlet: 5,
          totalNitrogenInlet: 9,
          totalPhosphorusInlet: 7,
          phInlet: 7.3,
          codOutlet: 18,
          bodOutlet: 3.2,
          suspendedSolidsOutlet: 3.5,
          ammoniaNitrogenOutlet: 2.2,
          totalNitrogenOutlet: 4.2,
          totalPhosphorusOutlet: 1.1,
          phOutlet: 7.1,
          flowRate: 1300,
          powerConsumption: 900,
          chemicalConsumption: 48,
          sludgeProduction: 13,
          operatorName: '王五',
          remarks: '雨天处理量增加',
          createTime: '2025-04-20 09:00:00'
        },
        {
          id: '4',
          stationId: 'yyyy-HM-dd-hhmmmss',
          stationName: '污水处理厂B',
          logDate: '2025-04-25',
          weather: '晴',
          codInlet: 49,
          bodInlet: 4,
          suspendedSolidsInlet: 4,
          ammoniaNitrogenInlet: 4,
          totalNitrogenInlet: 8,
          totalPhosphorusInlet: 7,
          phInlet: 7.2,
          codOutlet: 16,
          bodOutlet: 2.8,
          suspendedSolidsOutlet: 3.0,
          ammoniaNitrogenOutlet: 1.9,
          totalNitrogenOutlet: 3.8,
          totalPhosphorusOutlet: 1.0,
          phOutlet: 7.0,
          flowRate: 1250,
          powerConsumption: 875,
          chemicalConsumption: 46,
          sludgeProduction: 12.5,
          operatorName: '赵六',
          remarks: '正常运行',
          createTime: '2025-04-25 09:00:00'
        }
      ]

      // 根据查询条件过滤数据
      let filteredData = mockData
      if (params.stationId) {
        filteredData = filteredData.filter(item => item.stationId === params.stationId)
      }
      if (params.operatorName) {
        filteredData = filteredData.filter(item => item.operatorName.includes(params.operatorName))
      }

      const total = filteredData.length
      const pageSize = params.pageSize || 20
      const pageNum = params.pageNum || 1
      const startIndex = (pageNum - 1) * pageSize
      const endIndex = startIndex + pageSize
      const list = filteredData.slice(startIndex, endIndex)

      resolve({
        code: 200,
        message: 'success',
        data: {
          list,
          total,
          pageNum,
          pageSize
        }
      })
    }, 500)
  })

  // 实际API调用（暂时注释）
  // return request<ApiResponse<PageResponse<ProductionLogData>>>({
  //   url: '/istar/api/sewagePlant/productionLog/list',
  //   method: 'get',
  //   params: requestParams
  // })
}

/**
 * 获取生产日志详情
 * @param id 日志ID
 * @returns 
 */
export function getProductionLogDetail(id: string) {
  return request<ApiResponse<ProductionLogData>>({
    url: `/istar/api/sewagePlant/productionLog/${id}`,
    method: 'get'
  })
}

/**
 * 新增生产日志
 * @param data 日志数据
 * @returns
 */
export function createProductionLog(data: Omit<ProductionLogData, 'id' | 'createTime' | 'updateTime'>) {
  // 暂时返回模拟数据，实际项目中应该调用真实API
  return new Promise<ApiResponse<ProductionLogData>>((resolve) => {
    setTimeout(() => {
      const newData: ProductionLogData = {
        ...data,
        id: Date.now().toString(),
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString()
      }
      resolve({
        code: 200,
        message: '创建成功',
        data: newData
      })
    }, 500)
  })

  // 实际API调用（暂时注释）
  // return request<ApiResponse<ProductionLogData>>({
  //   url: '/istar/api/sewagePlant/productionLog',
  //   method: 'post',
  //   data
  // })
}

/**
 * 更新生产日志
 * @param id 日志ID
 * @param data 日志数据
 * @returns
 */
export function updateProductionLog(id: string, data: Partial<ProductionLogData>) {
  // 暂时返回模拟数据，实际项目中应该调用真实API
  return new Promise<ApiResponse<ProductionLogData>>((resolve) => {
    setTimeout(() => {
      const updatedData: ProductionLogData = {
        ...data,
        id,
        updateTime: new Date().toISOString()
      } as ProductionLogData
      resolve({
        code: 200,
        message: '更新成功',
        data: updatedData
      })
    }, 500)
  })

  // 实际API调用（暂时注释）
  // return request<ApiResponse<ProductionLogData>>({
  //   url: `/istar/api/sewagePlant/productionLog/${id}`,
  //   method: 'put',
  //   data
  // })
}

/**
 * 删除生产日志
 * @param ids 日志ID数组
 * @returns
 */
export function deleteProductionLog(ids: string[]) {
  // 暂时返回模拟数据，实际项目中应该调用真实API
  return new Promise<ApiResponse<void>>((resolve) => {
    setTimeout(() => {
      resolve({
        code: 200,
        message: '删除成功',
        data: undefined
      })
    }, 500)
  })

  // 实际API调用（暂时注释）
  // return request<ApiResponse<void>>({
  //   url: '/istar/api/sewagePlant/productionLog',
  //   method: 'delete',
  //   data: ids
  // })
}

/**
 * 获取生产日志统计数据
 * @param params 查询参数
 * @returns 
 */
export function getProductionLogStatistics(params?: {
  stationId?: string
  dateRange?: string[]
}) {
  const { dateRange, ...otherParams } = params || {}
  const requestParams = {
    ...otherParams,
    startDate: dateRange && dateRange[0] ? dateRange[0] : undefined,
    endDate: dateRange && dateRange[1] ? dateRange[1] : undefined
  }
  
  return request<ApiResponse<ProductionLogStatistics>>({
    url: '/istar/api/sewagePlant/productionLog/statistics',
    method: 'get',
    params: requestParams
  })
}

/**
 * 导出生产日志
 * @param params 导出参数
 * @returns 
 */
export function exportProductionLog(params: ExportParams) {
  const { dateRange, ...otherParams } = params
  const requestParams = {
    ...otherParams,
    startDate: dateRange && dateRange[0] ? dateRange[0] : undefined,
    endDate: dateRange && dateRange[1] ? dateRange[1] : undefined
  }
  
  return request({
    url: '/istar/api/sewagePlant/productionLog/export',
    method: 'get',
    params: requestParams,
    responseType: 'blob'
  })
}

/**
 * 批量导入生产日志
 * @param file 文件
 * @returns 
 */
export function importProductionLog(file: File) {
  const formData = new FormData()
  formData.append('file', file)
  
  return request<ApiResponse<{ successCount: number; failCount: number; errors?: string[] }>>({
    url: '/istar/api/sewagePlant/productionLog/import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
