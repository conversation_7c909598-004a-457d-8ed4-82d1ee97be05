<script lang="ts" setup>
import WMSLayer from '@arcgis/core/layers/WMSLayer.js';
import MapImageLayer from '@arcgis/core/layers/MapImageLayer';
import { queryLayerClassName } from '@/api/mapservice';
import { useGisStore } from '@/store';
import { useAnimatedLinesLayer } from '@/hooks/arcgis';
const emit = defineEmits(['pipe-loaded']);
const props = defineProps<{ disableLineAnimation?: boolean }>();
const gisStore = useGisStore();
const view: __esri.MapView | undefined = inject('view');
let pipeLayer;
if(GIS_SERVER_SWITCH){
  pipeLayer = new WMSLayer({
    id: 'pipelayer',
    title: '供水管网',
    url:'/geoserver/guazhou/wms',
    sublayers:[
      // { name: 'anqing' },
      { name: '管线',type:'MultiLineString',spatialReferences:'EPSG:3857'},
      { name: '测点' ,type:'Point',spatialReferences:'EPSG:3857'},
      { name: '测流井',type:'Point',spatialReferences:'EPSG:3857' },
      // { name: '计量装置',type:'Point',spatialReferences:'EPSG:3857'},
      // { name: '阀门',type:'Point',spatialReferences:'EPSG:3857' },
      // { name: '非控制阀' ,type:'Point',spatialReferences:'EPSG:3857'},
      // { name: '消防栓',type:'Point',spatialReferences:'EPSG:3857' },
      // { name: '检查井',type:'Point',spatialReferences:'EPSG:3857'},
    ],
    visible:true,
    version: "1.1.0", // WMS 版本
  });
}else{
  pipeLayer = new MapImageLayer({
    id: 'pipelayer',
    title: '管网',
    url:
      window.SITE_CONFIG.GIS_CONFIG.gisService +
      window.SITE_CONFIG.GIS_CONFIG.gisPipeDynamicService
  });
}

view?.map.add(pipeLayer);

const animatedLinesLayer = useAnimatedLinesLayer();

onMounted(async () => {
  try {
    await pipeLayer.when();
    if (!props.disableLineAnimation) {
      animatedLinesLayer.addTo(view);
    }
    const layerIds: number[] = [];
    pipeLayer?.allSublayers.map((item) => layerIds.push(item.id));
    await gisStore.Auth();
    const res = await queryLayerClassName(layerIds);
    gisStore.SET_gLayerInfoes(res.data?.result?.rows);
  } catch (error) {
    console.log(error);
    console.log('加载管网失败');
  }

  emit('pipe-loaded',pipeLayer);
});
</script>
<template>
  <div class="arcpipe"></div>
</template>
<style lang="scss" scoped></style>
