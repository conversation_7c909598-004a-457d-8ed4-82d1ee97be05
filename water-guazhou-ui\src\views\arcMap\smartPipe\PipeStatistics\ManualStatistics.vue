<!-- 通用统计 -->
<template>
  <RightDrawerMap
    ref="refMap"
    :title="'通用统计'"
    :full-content="true"
    @map-loaded="onMapLoaded"
  >
    <Form ref="refForm" :config="FormConfig"></Form>
    <template #detail-header>
      <span>统计结果</span>
    </template>
    <template #detail-default>
      <StatisticsCharts
        ref="refStatistic"
        :tabs="state.tabs"
        :view="staticState.view"
        :all-device="true"
        :query-params="staticState.queryParams"
        :statistics-params="getStatisticsParams()"
        :unit="getCurrentUnit()"
        prefix=""
        @detail-refreshed="state.loading = false"
        @attr-row-click="handleAttrRowClick"
        @bar-click="handleChartClick"
        @ring-click="handleChartClick"
      ></StatisticsCharts>
    </template>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import { useSketch } from '@/hooks/arcgis';
import { useGisStore } from '@/store';
import {
  EStatisticField,
  extentTo,
  getGraphicLayer,
  getLayerOids,
  getSubLayerIds
} from '@/utils/MapHelper';
import { SLMessage } from '@/utils/Message';
import {
  GetFieldConfig,
  GetFieldValueByGeoserver,
  QueryByPolygon
} from '@/utils/geoserver/wfsUtils';
import RightDrawerMap from '../../components/common/RightDrawerMap.vue';
import StatisticsCharts from '../../components/components/StatisticsCharts.vue';

const refMap = ref<InstanceType<typeof RightDrawerMap>>();
const refForm = ref<IFormIns>();
const refStatistic = ref<InstanceType<typeof StatisticsCharts>>();

const state = reactive<{
  tabs: any[];
  loading: boolean;
  layerInfos: any[];
  layerIds: any[];
  curType: 'ellipse' | 'rectangle' | 'polygon' | '';
  curOperate: string;
  curFieldNode?: any;
  curLayerFields: any[];
  uniqueing: boolean;
  statisticsResults: any[];
  selectedLayerId: string;
}>({
  tabs: [],
  curType: '',
  layerInfos: [],
  layerIds: [],
  loading: false,
  curOperate: '',
  curLayerFields: [],
  uniqueing: false,
  statisticsResults: [],
  selectedLayerId: ''
});
const staticState: {
  view?: __esri.MapView;
  graphicsLayer?: __esri.GraphicsLayer;
  sketch?: __esri.SketchViewModel;
  // drawer?: Draw
  // drawAction?: DrawAction
  queryParams: {
    geometry?: __esri.Geometry;
    where?: string;
  };
} = {
  queryParams: {
    geometry: undefined,
    where: '1=1'
  }
};
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fieldset: {
        desc: '绘制工具'
      },
      fields: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '绘制多边形',
              disabled: () => state.loading,
              iconifyIcon: 'mdi:shape-polygon-plus',
              click: () => initDraw('polygon'),
              styles: {
                flex: '1',
                minWidth: '0'
              }
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '绘制矩形',
              disabled: () => state.loading,
              iconifyIcon: 'ep:crop',
              click: () => initDraw('rectangle'),
              styles: {
                flex: '1',
                minWidth: '0'
              }
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '绘制圆形',
              disabled: () => state.loading,
              iconifyIcon: 'mdi:ellipse-outline',
              click: () => initDraw('circle'),
              styles: {
                flex: '1',
                minWidth: '0'
              }
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '清除图形',
              disabled: () => state.loading,
              iconifyIcon: 'ep:delete',
              click: () => clearGraphicsLayer(),
              styles: {
                flex: '1',
                minWidth: '0'
              }
            }
          ]
        }
      ]
    },
    {
      id: 'layerid',
      fieldset: {
        desc: '选择图层'
      },
      fields: [
        {
          type: 'tree',
          options: [],
          checkStrictly: true,
          showCheckbox: true,
          field: 'layerid',
          nodeKey: 'value',
          handleCheckChange: (data, isChecked) => {
            if (isChecked) {
              refForm.value && (refForm.value.dataForm.layerid = [data.value]);

              // 更新selectedLayerId状态来触发响应式更新
              state.selectedLayerId = data.value;
              debugger
              // 强制触发表单重新渲染，让统计类型字段显示/隐藏
              nextTick(() => {
                console.log('图层选择变化，当前选择:', state.selectedLayerId);
                // 触发FormConfig的响应式更新
                FormConfig.group = [...FormConfig.group];
              });
            }
          }
        }
      ]
    },
    {
      id: 'layer',
      fieldset: {
        desc: '图层字段'
      },
      fields: [
        {
          type: 'list',
          data: [],
          className: 'sql-list-wrapper',
          setData: async (config: IFormList, row) => {
            const layerid = row.layerid[0];
            const fields = await GetFieldConfig(layerid);
            if (
              fields &&
              fields.data &&
              fields.data.featureTypes &&
              fields.data.featureTypes[0]
            ) {
              // 获取字段列表
              const properties = fields.data.featureTypes[0].properties || [];
              config.data = properties;
              // 过滤字段
              const ignoredFields = [
                'START_SID',
                'END_SID',
                'SID',
                'OBJECTID',
                'PIPELENGTH',
                'X',
                'Y',
                'the_geom', // GeoServer特有的几何字段
                'geom' // GeoServer特有的几何字段
              ];

              // 创建字段列表
              const curLayerFields = properties
                .filter((item: any) => {
                  // 过滤出可编辑的字段
                  return (
                    ignoredFields.indexOf(item.name) === -1 &&
                    item.type !== 'gml:GeometryPropertyType' && // 排除几何字段
                    [
                      'int',
                      'long',
                      'double',
                      'float',
                      'string',
                      'date'
                    ].indexOf(item.type.split(':')[1]?.toLowerCase() || '') !==
                      -1
                  );
                })
                .map((item: any, index: number) => {
                  // 将GeoServer字段转换为表单项
                  const type = item.type.split(':')[1]?.toLowerCase() || '';
                  let fieldType = 'input';

                  if (['int', 'long', 'double', 'float'].includes(type)) {
                    fieldType = 'input-number';
                  } else if (type === 'date') {
                    fieldType = 'date';
                  }

                  return {
                    id: index,
                    field: item.name,
                    label: item.name,
                    type: fieldType,
                    disabled: false,
                    readonly: false
                  };
                })
                .sort((a: any, b: any) => {
                  return a.id - b.id;
                });

              state.curLayerFields = formatTree(curLayerFields || [], {
                id: 'field',
                label: 'label',
                value: 'field'
              });
              resetInlineFromConfig(curLayerFields);
            } else {
              console.error('无法获取GeoServer字段信息');
            }
          },
          setDataBy: 'layerid',
          displayField: 'name',
          valueField: 'name',
          highlightCurrentRow: true,
          nodeClick: (node) => {
            state.curFieldNode = node;
            appendSQL(`"${node.name}"`);
          }
        }
      ]
    },
    {
      id: 'field-construct',
      fieldset: {
        desc: '构建查询语句'
      },
      fields: [
        {
          type: 'btn-group',
          size: 'small',
          style: {
            width: '40%',
            display: 'flex',
            flexWrap: 'wrap'
          },
          className: 'sql-btns-wrapper',
          btns: [
            {
              perm: true,
              text: '=',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('=');
              }
            },
            {
              perm: true,
              text: '模糊',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL("like '%替换此处%'");
              }
            },
            {
              perm: true,
              text: '>',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('>');
              }
            },
            {
              perm: true,
              text: '<',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('<');
              }
            },
            {
              perm: true,
              text: '非',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('<>');
              }
            },
            {
              perm: true,
              text: '并且',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('and');
              }
            },
            {
              perm: true,
              text: '或者',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('or');
              }
            },
            {
              perm: true,
              text: '%',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('%');
              }
            }
          ],
          extraFormItem: [
            {
              type: 'list',
              wrapperStyle: {
                width: '60%',
                height: '144px'
              },
              className: 'sql-list-wrapper',
              field: 'uniqueValue',
              data: [],
              nodeClick: (node) => {
                appendSQL("'" + node + "'");
              },
              filters: [
                {
                  type: 'btn-group',
                  btns: [
                    {
                      perm: true,
                      text: () =>
                        state.curOperate === 'uniqueing'
                          ? '正在获取唯一值'
                          : '获取唯一值',
                      loading: () => state.curOperate === 'uniqueing',
                      disabled: () => state.curOperate === 'detailing',
                      styles: {
                        width: '100%',
                        borderRadius: '0'
                      },
                      click: () => getUniqueValue()
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    },
    {
      fieldset: {
        desc: '组合查询条件'
      },
      fields: [
        {
          type: 'textarea',
          field: 'sql',
          placeholder: 'OBJECTID > 0'
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '清除组合条件',
              type: 'danger',
              disabled: () => state.curOperate === 'detailing',
              click: () => clear(),
              styles: {
                width: '100%'
              }
            }
          ]
        }
      ]
    },
    {
      fieldset: {
        desc: '统计类型'
      },
      fields: [
        {
          type: 'select',
          field: 'statistic_type',
          clearable: false,
          options: [
            { label: '数量统计', value: 'count' },
            { 
              label: '管长统计', 
              value: 'length',
              disabled: () => state.selectedLayerId !== '管线'
            }
          ]
        }
      ]
    },
    {
      fieldset: {
        desc: '操作'
      },
      fields: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: () =>
                state.curOperate === 'detailing' ? '正在统计' : '统计',
              disabled: () => state.curOperate === 'detailing',
              loading: () => state.curOperate === 'detailing',
              click: () => startQuery(),
              styles: {
                width: '48%'
              }
            },
            {
              perm: true,
              text: '导出',
              type: 'primary',
              disabled: () => state.loading || state.tabs.length === 0,
              iconifyIcon: 'ep:download',
              click: () => exportStatistics(),
              styles: {
                width: '48%'
              }
            }
          ]
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12,
  defaultValue: {
    statistic_type: 'count'
  }
});

const handleAttrRowClick = (row?: any, tab?: string) => {
  console.log(row, tab);
  // const diameter = row.label
  // refreshDetailTable(diameter, tab)
};
const { initSketch, destroySketch } = useSketch();
const initDraw = (type: any) => {
  clearGraphicsLayer();
  staticState.sketch?.create(type);
};
const clearGraphicsLayer = () => {
  staticState.graphicsLayer?.removeAll();
  staticState.queryParams.geometry = undefined;
};

const appendSQL = (val) => {
  if (!refForm.value) return;
  if (!refForm.value?.dataForm) refForm.value.dataForm = {};
  const sql = refForm.value.dataForm.sql || ' ';
  refForm.value.dataForm.sql = sql + val + ' ';
};

const clear = () => {
  refForm.value?.dataForm && (refForm.value.dataForm.sql = '');
};

// 添加缺失的工具函数
const formatTree = (data: any[], config: any) => {
  return data.map((item) => ({
    ...item,
    [config.id]: item[config.value],
    [config.label]: item[config.label],
    [config.value]: item[config.value]
  }));
};

const resetInlineFromConfig = (fields: any[]) => {
  // 重置内联表单配置
  console.log('重置表单配置:', fields);
};

const getUniqueValue = async () => {
  if (!state.curFieldNode) {
    SLMessage.warning('请先选择一个字段');
    return;
  }

  state.curOperate = 'uniqueing';
  state.uniqueing = true;

  try {
    // 获取图层名称
    const layerName = refForm.value?.dataForm.layerid?.[0];
    const fieldName = state.curFieldNode.name;

    if (!layerName || !fieldName) {
      SLMessage.warning('请先选择图层和字段');
      return;
    }

    // 调用GeoServer API获取唯一值
    const response = await GetFieldValueByGeoserver({
      layerName: layerName,
      fiedName: fieldName
    });

    // 检查响应是否有效
    if (!response || !response.data) {
      console.error('获取GeoServer唯一值响应无效:', response);
      SLMessage.error('获取唯一值失败');
      return;
    }

    const data = response.data;
    console.log('获取到的GeoServer数据:', data);

    // 检查数据是否有效
    if (!data.features || !Array.isArray(data.features)) {
      console.error('无效的GeoServer数据格式:', data);
      SLMessage.warning('无法获取唯一值');
      return;
    }

    // 创建一个空集合来存储唯一的字段值
    const uniqueValues = new Set();

    // 遍历特征数组
    data.features.forEach((feature: any) => {
      if (feature && feature.properties) {
        // 添加字段值到集合中
        const value = feature.properties[fieldName];
        if (value !== null && value !== undefined && value !== '') {
          uniqueValues.add(value);
        }
      }
    });

    // 转换为数组并排序
    const uniqueValuesArray = Array.from(uniqueValues).sort();
    console.log('提取的唯一值:', uniqueValuesArray);

    // 设置列表数据
    const extraFormItem = FormConfig.group.find(
      (item) => item.id === 'field-construct'
    )?.fields[0].extraFormItem;
    const field = extraFormItem && (extraFormItem[0] as IFormList);
    if (field) {
      field.data = uniqueValuesArray;
    }

    SLMessage.success(`获取到 ${uniqueValuesArray.length} 个唯一值`);
  } catch (error) {
    console.error('获取唯一值失败:', error);
    SLMessage.error('获取唯一值失败');
  } finally {
    state.curOperate = '';
    state.uniqueing = false;
  }
};

const getLayerInfo = async () => {
  const field = FormConfig.group.find((item) => item.id === 'layerid')
    ?.fields[0] as IFormTree;
  const layerInfo = staticState.view?.layerViews.items[0].layer.sublayers;
  let layers = layerInfo.items.map((item) => {
    return {
      label: item.name,
      value: item.name,
      layername: item.name,
      type: item.type,
      spatialReferences: item.spatialReferences
    };
  });
  field.options = layers; // [{ label: '管线类', value: -2, children: layers }]
};

// 导出查询结果
const exportStatistics = async () => {
  try {
    if (state.tabs.length === 0 || !state.tabs[0]?.data?.length) {
      SLMessage.warning('暂无查询数据可导出');
      return;
    }

    // 动态导入xlsx库
    let XLSX: any;
    try {
      XLSX = await import('xlsx');
    } catch (error) {
      SLMessage.error('请先安装xlsx库: npm install xlsx');
      return;
    }

    // 创建工作簿
    const wb = XLSX.utils.book_new();

    // 获取当前查询参数
    const formData = refForm.value?.dataForm;
    const layerName = formData?.layerid?.[0] || '未知图层';
    const queryCondition = formData?.sql || '1=1';
    const data = state.tabs[0].data;

    // 获取所有字段名（排除geometry字段）
    const allFields = new Set<string>();
    data.forEach((item: any) => {
      Object.keys(item).forEach((key) => {
        if (key !== 'geometry') {
          allFields.add(key);
        }
      });
    });

    const fields = Array.from(allFields);

    // 创建表头
    const headers = fields.map((field) => {
      // 字段名映射
      const fieldMap: Record<string, string> = {
        OBJECTID: '对象ID',
        管材: '管材',
        管径: '管径',
        备注: '备注',
        fid: 'ID'
      };
      return fieldMap[field] || field;
    });

    // 创建数据行
    const rows = data.map((item: any) => {
      return fields.map((field) => {
        const value = item[field];
        return value !== undefined && value !== null ? String(value) : '';
      });
    });

    // 创建查询结果数据
    const queryData = [
      ['管网资产查询报告'],
      [''],
      ['查询参数'],
      ['图层名称', layerName],
      ['查询条件', queryCondition],
      ['查询时间', new Date().toLocaleString()],
      ['记录总数', data.length],
      [''],
      ['查询结果'],
      headers,
      ...rows
    ];

    // 创建详细数据工作表
    const ws = XLSX.utils.aoa_to_sheet(queryData);
    XLSX.utils.book_append_sheet(wb, ws, '查询结果');

    // 生成文件名
    const now = new Date();
    const timestamp = now
      .toISOString()
      .slice(0, 19)
      .replace(/[:-]/g, '')
      .replace('T', '_');
    const filename = `管网资产查询_${layerName}_${timestamp}.xlsx`;

    // 导出Excel文件
    XLSX.writeFile(wb, filename);

    SLMessage.success('查询结果导出成功');
  } catch (error) {
    console.error('导出失败:', error);
    SLMessage.error('导出失败，请重试');
  }
};

// 添加图表点击处理方法
const handleChartClick = (params: any) => {
  const tab = params.name;
  refStatistic.value?.setCurLayer(tab);
};

// 获取当前统计类型对应的单位
const getCurrentUnit = () => {
  const formData = refForm.value?.dataForm;
  const statisticType = formData?.statistic_type || 'count';
  const selectedLayerId = state.selectedLayerId;

  // 如果是管线图层且选择了管长统计，返回'm'
  if (selectedLayerId === '管线' && statisticType === 'length') {
    return 'm';
  }

  // 其他情况返回'个'
  return '个';
};

// 获取StatisticsCharts组件需要的统计参数
const getStatisticsParams = () => {
  const formData = refForm.value?.dataForm;
  const statisticType = formData?.statistic_type || 'count';
  const selectedLayerId = state.selectedLayerId;

  // 根据统计类型确定统计字段和类型
  let statistic_field = EStatisticField.OBJECTID; // 默认按OBJECTID统计（数量）
  let statistic_type_value: '1' | '2' = '1'; // 默认数量统计

  if (selectedLayerId === '管线' && statisticType === 'length') {
    statistic_field = EStatisticField.ShapeLen; 
    statistic_type_value = '2'; // 长度统计
  }

  return {
    group_fields: ['管材', '管径', '备注'], // 分组字段
    statistic_field: statistic_field,
    statistic_type: statistic_type_value
  };
};

// 切换统计类型的处理方法
const switchStatisticType = (statisticType: string) => {
  console.log('切换统计类型:', statisticType);

  if (state.tabs.length === 0) {
    return;
  }

  // 根据统计类型筛选显示的tabs
  const allTabs = state.tabs;
  let filteredTabs: any[] = [];

  if (statisticType === 'count') {
    // 显示数量统计相关的tabs
    filteredTabs = allTabs.filter(
      (tab) => tab.name.includes('数量') || !tab.name.includes('管长')
    );
  } else if (statisticType === 'length') {
    // 显示管长统计相关的tabs
    filteredTabs = allTabs.filter((tab) => tab.name.includes('管长'));
  }

  // 如果有筛选结果，更新StatisticsCharts组件显示的tabs
  if (filteredTabs.length > 0) {
    // 临时更新tabs来触发StatisticsCharts组件重新渲染
    const originalTabs = [...state.tabs];
    state.tabs = filteredTabs;

    nextTick(() => {
      refStatistic.value?.refreshChar();
      // 恢复完整的tabs数据，但保持当前显示状态
      state.tabs = originalTabs;
    });
  }
};

// 简化的startQuery方法，直接根据组合条件查询并统计
const startQuery = async () => {
  try {
    // 验证必要参数
    const formData = refForm.value?.dataForm;
    if (!formData?.layerid?.length) {
      SLMessage.warning('请选择一个要查询的图层');
      return;
    }

    SLMessage.info('正在查询，请稍候...');
    state.curOperate = 'detailing';
    state.tabs.length = 0;

    // 获取查询参数
    const layerName = formData.layerid[0];
    const queryCondition = formData.sql || '1=1';

    // 获取多边形坐标（可选）
    let polygonCoordinates: number[][] | null = null;
    const geometry = staticState.queryParams.geometry;

    if (geometry) {
      if (
        geometry.type === 'polygon' &&
        geometry.rings &&
        geometry.rings.length > 0
      ) {
        // 获取外环坐标
        polygonCoordinates = geometry.rings[0];
      } else if (geometry.type === 'extent') {
        // 如果是矩形，转换为多边形坐标
        const { xmin, ymin, xmax, ymax } = geometry;
        polygonCoordinates = [
          [xmin, ymin],
          [xmax, ymin],
          [xmax, ymax],
          [xmin, ymax],
          [xmin, ymin]
        ];
      }
    }

    // 调用QueryByPolygon获取数据（如果没有几何图形，传null进行全图层查询）
    const response = await QueryByPolygon(
      layerName,
      polygonCoordinates,
      queryCondition
    );

    // 检查响应有效性
    if (!response || !response.data) {
      console.error('QueryByPolygon响应无效:', response);
      SLMessage.error('查询失败，请重试');
      return;
    }

    const features = response.data.features || [];

    if (features.length === 0) {
      SLMessage.warning('查询区域内没有找到符合条件的数据');
      return;
    }

    // 处理要素数据，添加必要的属性
    const processedFeatures = features.map((feature: any, index: number) => ({
      ...feature.properties,
      OBJECTID: feature.properties.OBJECTID || index + 1,
      geometry: feature.geometry
    }));

    if (layerName === '管线') {
        const data = processedFeatures.map((feature: any) => ({
          ...feature,
        }));

        state.tabs = [
          {
            label: `${layerName}`,
            name: `${layerName}`,
            value: `${layerName}`,
            data: data
          }
        ];
    } else {
      // 其他图层：只显示数量统计
      state.tabs = [
        {
          label: layerName,
          name: layerName,
          value: layerName,
          data: processedFeatures
        }
      ];
    }

    // 显示详情面板并刷新StatisticsCharts组件
    refMap.value?.toggleCustomDetail(true);

    // 等待组件渲染完成后再刷新图表
    await nextTick();

    // 延迟刷新，确保StatisticsCharts组件完全初始化
    setTimeout(() => {
      // 先尝试初始化统计，再刷新图表
      if (refStatistic.value) {
        // 如果有initTab方法，先调用它
        if (typeof refStatistic.value.initTab === 'function') {
          refStatistic.value.initTab();
        }
        // 然后刷新图表
        refStatistic.value.refreshChar();
      }
    }, 500);

    // 更新查询参数供StatisticsCharts组件使用
    staticState.queryParams.where = queryCondition;

    console.log('查询完成:', {
      layerName,
      featuresCount: processedFeatures.length,
      tabs: state.tabs,
      sampleData: processedFeatures.slice(0, 2), // 显示前2条数据样本
      availableFields:
        processedFeatures.length > 0 ? Object.keys(processedFeatures[0]) : []
    });

    SLMessage.success(`查询完成！共找到 ${features.length} 条记录`);
  } catch (error: any) {
    console.error('查询失败:', error);
    SLMessage.error(error.message || '查询失败，请重试');
  } finally {
    state.curOperate = '';
  }
};
const resolveDrawEnd = (result: ISketchHandlerParameter) => {
  staticState.queryParams.geometry = result.graphics[0]?.geometry;
};
const onMapLoaded = (view) => {
  staticState.view = view;
  staticState.graphicsLayer = getGraphicLayer(staticState.view, {
    id: 'search-manual',
    title: '高级统计'
  });
  staticState.sketch = initSketch(staticState.view, staticState.graphicsLayer, {
    createCallBack: resolveDrawEnd,
    updateCallBack: resolveDrawEnd
  });
  setTimeout(() => {
    getLayerInfo();
  }, 1000);
};
onBeforeUnmount(() => {
  destroySketch();
  staticState.sketch = undefined;
});
</script>
<style lang="scss" scoped></style>
