import { useAppStore } from '@/store'

/**
 * 统一的图表配置工具
 * 提供现代化、美观的图表样式配置
 */

// 现代化配色方案
export const CHART_COLORS = {
  primary: [
    '#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', 
    '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc', '#ff9f7f'
  ],
  gradient: {
    blue: ['#188df0', '#83bff6'],
    green: ['#91cc75', '#c7e9b4'],
    orange: ['#fac858', '#fde68a'],
    red: ['#ee6666', '#fca5a5'],
    purple: ['#9a60b4', '#c4b5fd']
  }
}

// 获取主题相关的颜色
export const getThemeColors = () => {
  const isDark = useAppStore().isDark
  return {
    background: isDark ? '#1a202c' : '#ffffff',
    cardBackground: isDark ? '#2d3748' : '#f7fafc',
    textPrimary: isDark ? '#ffffff' : '#2d3748',
    textSecondary: isDark ? '#a0aec0' : '#4a5568',
    borderColor: isDark ? '#4a5568' : '#e2e8f0',
    gridColor: isDark ? '#2d3748' : '#e2e8f0'
  }
}

// 通用的图表基础配置
export const getBaseChartConfig = () => {
  const colors = getThemeColors()
  return {
    backgroundColor: 'transparent',
    textStyle: {
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
    },
    animation: true,
    animationDuration: 1000,
    animationEasing: 'cubicOut'
  }
}

// 通用的网格配置
export const getGridConfig = (options?: {
  left?: string | number
  right?: string | number
  top?: string | number
  bottom?: string | number
}) => {
  return {
    left: options?.left || '10%',
    right: options?.right || '10%',
    top: options?.top || '15%',
    bottom: options?.bottom || '15%',
    containLabel: true
  }
}

// 通用的tooltip配置
export const getTooltipConfig = (formatter?: (params: any) => string) => {
  return {
    trigger: 'axis',
    backgroundColor: 'rgba(50, 50, 50, 0.9)',
    borderColor: 'transparent',
    borderRadius: 8,
    textStyle: {
      color: '#fff',
      fontSize: 12
    },
    padding: [8, 12],
    formatter: formatter || undefined
  }
}

// 通用的图例配置
export const getLegendConfig = (options?: {
  orient?: 'horizontal' | 'vertical'
  position?: 'top' | 'bottom' | 'left' | 'right'
  align?: 'left' | 'center' | 'right'
}) => {
  const colors = getThemeColors()
  const position = options?.position || 'top'
  
  let positionConfig = {}
  switch (position) {
    case 'top':
      positionConfig = { top: 10, left: 'center' }
      break
    case 'bottom':
      positionConfig = { bottom: 10, left: 'center' }
      break
    case 'left':
      positionConfig = { left: 10, top: 'center' }
      break
    case 'right':
      positionConfig = { right: 10, top: 'center' }
      break
  }

  return {
    ...positionConfig,
    orient: options?.orient || (position === 'left' || position === 'right' ? 'vertical' : 'horizontal'),
    itemWidth: 12,
    itemHeight: 12,
    itemGap: 8,
    textStyle: {
      color: colors.textSecondary,
      fontSize: 12
    }
  }
}

// 通用的坐标轴配置
export const getAxisConfig = (type: 'category' | 'value', options?: {
  name?: string
  data?: string[]
  showGrid?: boolean
  showLine?: boolean
}) => {
  const colors = getThemeColors()
  
  const baseConfig = {
    axisLine: {
      show: options?.showLine !== false,
      lineStyle: {
        color: colors.borderColor
      }
    },
    axisTick: {
      show: false
    },
    axisLabel: {
      color: colors.textSecondary,
      fontSize: 12
    }
  }

  if (type === 'category') {
    return {
      type: 'category',
      data: options?.data || [],
      ...baseConfig
    }
  } else {
    return {
      type: 'value',
      name: options?.name || '',
      nameTextStyle: {
        color: colors.textSecondary,
        fontSize: 12
      },
      splitLine: {
        show: options?.showGrid !== false,
        lineStyle: {
          color: colors.gridColor,
          type: 'dashed'
        }
      },
      ...baseConfig
    }
  }
}

/**
 * 创建现代化柱状图配置
 */
export const createBarChart = (data: Array<{key: string, value: number}>, options?: {
  title?: string
  unit?: string
  horizontal?: boolean
  gradient?: boolean
  showLabel?: boolean
}) => {
  const colors = getThemeColors()
  const values = data.map(item => item.value)
  const labels = data.map(item => item.key)
  
  const isHorizontal = options?.horizontal || false
  const useGradient = options?.gradient !== false
  
  return {
    ...getBaseChartConfig(),
    title: options?.title ? {
      text: options.title,
      left: 'center',
      top: 10,
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal',
        color: colors.textPrimary
      }
    } : undefined,
    tooltip: getTooltipConfig((params: any) => {
      const param = Array.isArray(params) ? params[0] : params
      const total = values.reduce((a, b) => a + b, 0)
      const percentage = total > 0 ? ((param.value / total) * 100).toFixed(1) : '0'
      return `${param.name}<br/>${param.value}${options?.unit || ''} (${percentage}%)`
    }),
    grid: getGridConfig({ top: options?.title ? '20%' : '10%' }),
    xAxis: isHorizontal ? getAxisConfig('value', { name: options?.unit }) : getAxisConfig('category', { data: labels }),
    yAxis: isHorizontal ? getAxisConfig('category', { data: labels }) : getAxisConfig('value', { name: options?.unit }),
    series: [{
      type: 'bar',
      data: values.map((value, index) => ({
        value,
        itemStyle: {
          color: useGradient ? {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: CHART_COLORS.primary[index % CHART_COLORS.primary.length] },
              { offset: 1, color: CHART_COLORS.primary[index % CHART_COLORS.primary.length] + '80' }
            ]
          } : CHART_COLORS.primary[index % CHART_COLORS.primary.length],
          borderRadius: isHorizontal ? [0, 4, 4, 0] : [4, 4, 0, 0]
        }
      })),
      barWidth: '60%',
      label: options?.showLabel !== false ? {
        show: true,
        position: isHorizontal ? 'right' : 'top',
        color: colors.textPrimary,
        fontSize: 11,
        formatter: '{c}'
      } : undefined,
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.3)'
        }
      }
    }]
  }
}

/**
 * 创建现代化饼图配置
 */
export const createPieChart = (data: Array<{name: string, value: number}>, options?: {
  title?: string
  unit?: string
  radius?: [string, string]
  showCenter?: boolean
  showLegend?: boolean
}) => {
  const colors = getThemeColors()
  const total = data.reduce((sum, item) => sum + item.value, 0)
  
  const seriesData = data.map((item, index) => ({
    name: item.name,
    value: item.value,
    itemStyle: {
      color: CHART_COLORS.primary[index % CHART_COLORS.primary.length]
    }
  }))

  return {
    ...getBaseChartConfig(),
    title: options?.title ? {
      text: options.title,
      left: 'center',
      top: 10,
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal',
        color: colors.textPrimary
      }
    } : undefined,
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(50, 50, 50, 0.9)',
      borderColor: 'transparent',
      textStyle: {
        color: '#fff'
      },
      formatter: (params: any) => {
        const percentage = total > 0 ? ((params.value / total) * 100).toFixed(1) : '0'
        return `${params.name}<br/>${params.value}${options?.unit || ''} (${percentage}%)`
      }
    },
    legend: options?.showLegend !== false ? {
      ...getLegendConfig({ position: 'right', orient: 'vertical' }),
      formatter: (name: string) => {
        const item = data.find(d => d.name === name)
        if (item && total > 0) {
          const percentage = ((item.value / total) * 100).toFixed(1)
          return `${name} ${percentage}%`
        }
        return name
      }
    } : undefined,
    series: [{
      type: 'pie',
      radius: options?.radius || ['45%', '70%'],
      center: options?.showLegend !== false ? ['35%', '55%'] : ['50%', '55%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 6,
        borderColor: colors.background,
        borderWidth: 2
      },
      label: {
        show: false
      },
      labelLine: {
        show: false
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.3)'
        },
        label: {
          show: true,
          fontSize: 14,
          fontWeight: 'bold',
          color: colors.textPrimary,
          formatter: (params: any) => {
            const percentage = total > 0 ? ((params.value / total) * 100).toFixed(1) : '0'
            return `${params.name}\n${params.value}${options?.unit || ''}\n${percentage}%`
          }
        }
      },
      data: seriesData
    }],
    graphic:  {
      type: 'text',
      left: options?.showLegend !== false ? '30%' : '50%',
      top: '50%',
      style: {
        text: `总计\n${total}${options?.unit || ''}`,
        textAlign: 'center',
        fill: colors.textPrimary,
        fontSize: 14,
        fontWeight: 'bold'
      }
    }
  }
}

/**
 * 创建现代化折线图配置
 */
export const createLineChart = (data: Array<{
  name: string
  data: Array<{x: string, y: number}>
}>, options?: {
  title?: string
  unit?: string
  smooth?: boolean
  area?: boolean
  showPoints?: boolean
}) => {
  const colors = getThemeColors()
  const xData = data[0]?.data.map(item => item.x) || []
  
  const series = data.map((item, index) => ({
    name: item.name,
    type: 'line',
    data: item.data.map(d => d.y),
    smooth: options?.smooth !== false,
    symbol: options?.showPoints !== false ? 'circle' : 'none',
    symbolSize: 6,
    lineStyle: {
      width: 2,
      color: CHART_COLORS.primary[index % CHART_COLORS.primary.length]
    },
    itemStyle: {
      color: CHART_COLORS.primary[index % CHART_COLORS.primary.length]
    },
    areaStyle: options?.area ? {
      color: {
        type: 'linear',
        x: 0, y: 0, x2: 0, y2: 1,
        colorStops: [
          { offset: 0, color: CHART_COLORS.primary[index % CHART_COLORS.primary.length] + '40' },
          { offset: 1, color: CHART_COLORS.primary[index % CHART_COLORS.primary.length] + '10' }
        ]
      }
    } : undefined
  }))

  return {
    ...getBaseChartConfig(),
    title: options?.title ? {
      text: options.title,
      left: 'center',
      top: 10,
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal',
        color: colors.textPrimary
      }
    } : undefined,
    tooltip: getTooltipConfig(),
    legend: data.length > 1 ? getLegendConfig() : undefined,
    grid: getGridConfig({ top: options?.title ? '20%' : '10%' }),
    xAxis: getAxisConfig('category', { data: xData }),
    yAxis: getAxisConfig('value', { name: options?.unit }),
    series
  }
}

/**
 * 创建仪表盘图配置
 */
export const createGaugeChart = (value: number, options?: {
  title?: string
  unit?: string
  max?: number
  min?: number
  thresholds?: Array<{value: number, color: string}>
}) => {
  const colors = getThemeColors()
  const max = options?.max || 100
  const min = options?.min || 0
  
  return {
    ...getBaseChartConfig(),
    title: options?.title ? {
      text: options.title,
      left: 'center',
      top: 10,
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal',
        color: colors.textPrimary
      }
    } : undefined,
    series: [{
      type: 'gauge',
      center: ['50%', '60%'],
      radius: '80%',
      min,
      max,
      splitNumber: 5,
      axisLine: {
        lineStyle: {
          width: 8,
          color: [
            [0.3, '#91cc75'],
            [0.7, '#fac858'],
            [1, '#ee6666']
          ]
        }
      },
      pointer: {
        itemStyle: {
          color: colors.textPrimary
        }
      },
      axisTick: {
        distance: -8,
        length: 8,
        lineStyle: {
          color: colors.textSecondary,
          width: 1
        }
      },
      splitLine: {
        distance: -12,
        length: 12,
        lineStyle: {
          color: colors.textSecondary,
          width: 2
        }
      },
      axisLabel: {
        color: colors.textSecondary,
        fontSize: 12,
        distance: -20
      },
      detail: {
        valueAnimation: true,
        formatter: `{value}${options?.unit || ''}`,
        color: colors.textPrimary,
        fontSize: 20,
        offsetCenter: [0, '70%']
      },
      data: [{ value }]
    }]
  }
}

/**
 * 创建散点图配置
 */
export const createScatterChart = (data: Array<{
  name: string
  data: Array<{x: number, y: number, size?: number}>
}>, options?: {
  title?: string
  xAxisName?: string
  yAxisName?: string
  showRegression?: boolean
}) => {
  const colors = getThemeColors()
  
  const series = data.map((item, index) => ({
    name: item.name,
    type: 'scatter',
    data: item.data.map(d => [d.x, d.y, d.size || 10]),
    symbolSize: (data: number[]) => data[2] || 8,
    itemStyle: {
      color: CHART_COLORS.primary[index % CHART_COLORS.primary.length],
      opacity: 0.8
    },
    emphasis: {
      itemStyle: {
        shadowBlur: 10,
        shadowColor: 'rgba(0, 0, 0, 0.3)'
      }
    }
  }))

  return {
    ...getBaseChartConfig(),
    title: options?.title ? {
      text: options.title,
      left: 'center',
      top: 10,
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal',
        color: colors.textPrimary
      }
    } : undefined,
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(50, 50, 50, 0.9)',
      borderColor: 'transparent',
      textStyle: {
        color: '#fff'
      },
      formatter: (params: any) => {
        return `${params.seriesName}<br/>${options?.xAxisName || 'X'}: ${params.data[0]}<br/>${options?.yAxisName || 'Y'}: ${params.data[1]}`
      }
    },
    legend: data.length > 1 ? getLegendConfig() : undefined,
    grid: getGridConfig({ top: options?.title ? '20%' : '10%' }),
    xAxis: getAxisConfig('value', { name: options?.xAxisName }),
    yAxis: getAxisConfig('value', { name: options?.yAxisName }),
    series
  }
}

/**
 * 创建雷达图配置
 */
export const createRadarChart = (data: Array<{
  name: string
  value: number[]
}>, indicators: Array<{name: string, max: number}>, options?: {
  title?: string
  shape?: 'polygon' | 'circle'
}) => {
  const colors = getThemeColors()
  
  return {
    ...getBaseChartConfig(),
    title: options?.title ? {
      text: options.title,
      left: 'center',
      top: 10,
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal',
        color: colors.textPrimary
      }
    } : undefined,
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(50, 50, 50, 0.9)',
      borderColor: 'transparent',
      textStyle: {
        color: '#fff'
      }
    },
    legend: {
      ...getLegendConfig(),
      top: options?.title ? '15%' : '5%'
    },
    radar: {
      indicator: indicators,
      shape: options?.shape || 'polygon',
      center: ['50%', '60%'],
      radius: '70%',
      axisName: {
        color: colors.textSecondary,
        fontSize: 12
      },
      splitLine: {
        lineStyle: {
          color: colors.gridColor
        }
      },
      splitArea: {
        show: false
      },
      axisLine: {
        lineStyle: {
          color: colors.gridColor
        }
      }
    },
    series: [{
      type: 'radar',
      data: data.map((item, index) => ({
        value: item.value,
        name: item.name,
        itemStyle: {
          color: CHART_COLORS.primary[index % CHART_COLORS.primary.length]
        },
        areaStyle: {
          color: CHART_COLORS.primary[index % CHART_COLORS.primary.length] + '20'
        }
      }))
    }]
  }
}

/**
 * 创建热力图配置
 */
export const createHeatmapChart = (data: Array<[number, number, number]>, options?: {
  title?: string
  xAxisData?: string[]
  yAxisData?: string[]
  visualMap?: {min: number, max: number}
}) => {
  const colors = getThemeColors()
  
  return {
    ...getBaseChartConfig(),
    title: options?.title ? {
      text: options.title,
      left: 'center',
      top: 10,
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal',
        color: colors.textPrimary
      }
    } : undefined,
    tooltip: {
      position: 'top',
      backgroundColor: 'rgba(50, 50, 50, 0.9)',
      borderColor: 'transparent',
      textStyle: {
        color: '#fff'
      }
    },
    grid: getGridConfig({ top: options?.title ? '20%' : '10%', bottom: '20%' }),
    xAxis: {
      type: 'category',
      data: options?.xAxisData || [],
      splitArea: {
        show: true
      },
      axisLabel: {
        color: colors.textSecondary
      }
    },
    yAxis: {
      type: 'category',
      data: options?.yAxisData || [],
      splitArea: {
        show: true
      },
      axisLabel: {
        color: colors.textSecondary
      }
    },
    visualMap: {
      min: options?.visualMap?.min || 0,
      max: options?.visualMap?.max || 100,
      calculable: true,
      orient: 'horizontal',
      left: 'center',
      bottom: '5%',
      textStyle: {
        color: colors.textSecondary
      }
    },
    series: [{
      type: 'heatmap',
      data: data,
      label: {
        show: true,
        color: colors.textPrimary
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  }
}

/**
 * 图表工厂函数 - 根据类型和数据自动创建图表配置
 */
export const createChart = (
  type: 'bar' | 'pie' | 'line' | 'gauge' | 'scatter' | 'radar' | 'heatmap',
  data: any,
  options?: any
) => {
  switch (type) {
    case 'bar':
      return createBarChart(data, options)
    case 'pie':
      return createPieChart(data, options)
    case 'line':
      return createLineChart(data, options)
    case 'gauge':
      return createGaugeChart(data, options)
    case 'scatter':
      return createScatterChart(data, options)
    case 'radar':
      return createRadarChart(data.data, data.indicators, options)
    case 'heatmap':
      return createHeatmapChart(data, options)
    default:
      throw new Error(`Unsupported chart type: ${type}`)
  }
}

/**
 * 兼容旧版本的图表配置函数
 * @deprecated 请使用新的 createChart 函数
 */
export const zhuzhuangtu = (data: any[], unit?: string) => {
  console.warn('zhuzhuangtu is deprecated, please use createBarChart instead')
  return createBarChart(data.map(item => ({ key: item.key, value: item.value })), { unit })
}

/**
 * 兼容旧版本的环形图配置函数
 * @deprecated 请使用新的 createChart 函数
 */
export const ring = (data: any[], unit?: string, prefix?: string, precision = 2) => {
  console.warn('ring is deprecated, please use createPieChart instead')
  return createPieChart(data.map(item => ({ name: item.name, value: parseInt(item.value) })), { unit })
}