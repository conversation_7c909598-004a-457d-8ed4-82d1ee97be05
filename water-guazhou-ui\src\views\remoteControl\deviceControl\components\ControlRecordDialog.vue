<template>
  <el-dialog 
    :title="dialogTitle" 
    v-model="visible" 
    width="600px" 
    append-to-body
    @close="handleClose"
  >
    <el-form ref="formRef" :model="form" :rules="formRules" label-width="100px">
      <el-form-item label="设备信息">
        <div class="device-info">
          <p><strong>设备名称：</strong>{{ deviceInfo?.name || '-' }}</p>
          <p><strong>设备编号：</strong>{{ extractDeviceId(deviceInfo) || '-' }}</p>
          <p><strong>设备类型：</strong>{{ deviceInfo?.deviceTypeName || '-' }}</p>
          <p><strong>安装位置：</strong>{{ deviceInfo?.location || '-' }}</p>
        </div>
      </el-form-item>
      
      <el-form-item label="控制类型" prop="controlType">
        <el-select v-model="form.controlType" placeholder="请选择控制类型" style="width: 100%">
          <el-option 
            v-for="option in controlTypeOptions" 
            :key="option.value" 
            :label="option.label" 
            :value="option.value"
          ></el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item label="控制值" prop="controlValue">
        <el-input 
          v-model="form.controlValue" 
          placeholder="请输入控制值（如需要）"
          :disabled="!needControlValue"
        >
          <template #append v-if="controlValueUnit">
            {{ controlValueUnit }}
          </template>
        </el-input>
        <div class="form-tip" v-if="controlValueTip">
          <el-icon><InfoFilled /></el-icon>
          {{ controlValueTip }}
        </div>
      </el-form-item>
      
      <el-form-item label="控制描述" prop="controlDescription">
        <el-input 
          v-model="form.controlDescription" 
          type="textarea" 
          :rows="3"
          placeholder="请输入控制描述"
          maxlength="200"
          show-word-limit
        ></el-input>
      </el-form-item>
      
      <el-form-item label="操作人员">
        <el-input v-model="form.operatorName" disabled></el-input>
      </el-form-item>
      
      <el-form-item label="控制时间">
        <el-date-picker
          v-model="form.controlTime"
          type="datetime"
          placeholder="选择控制时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 100%"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="控制密钥" :rules="controlKeyRules">
        <el-input
          v-model="controlKey"
          placeholder="请输入控制密钥"
          maxlength="50"
        >
          <template #prepend>
            <el-icon><Lock /></el-icon>
          </template>
        </el-input>
        <div class="form-tip">
          <el-icon><InfoFilled /></el-icon>
          为确保操作安全，请输入控制密钥进行身份验证
        </div>
      </el-form-item>

      <el-form-item label="备注">
        <el-input 
          v-model="form.remark" 
          type="textarea" 
          :rows="2"
          placeholder="请输入备注（可选）"
          maxlength="100"
          show-word-limit
        ></el-input>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ submitting ? '保存中...' : '确 定' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
.form-tip {
  margin-top: 5px;
  font-size: 12px;
  color: #909399;
  display: flex;
  align-items: center;
  gap: 4px;
}

.form-tip .el-icon {
  font-size: 14px;
}

.el-input-group__prepend .el-icon {
  color: #409eff;
}
</style>

<script lang="ts">
import { defineComponent, ref, reactive, computed, watch, toRefs } from 'vue';

import { InfoFilled, Lock } from '@element-plus/icons-vue';
import type { FormInstance } from 'element-plus';
import { ElMessage } from 'element-plus';
import { useUserStore } from '@/store/modules/user';
import { saveDeviceControlRecord } from '@/api/remoteControl';

// 设备类型映射
const DEVICE_TYPE_MAP = {
  pump: '水泵',
  valve: '阀门',
  monitor: '监测设备',
  controller: '控制器'
};

// 设备状态映射
const DEVICE_STATUS_MAP = {
  1: '在线',
  0: '离线',
  2: '故障',
  3: '维护中'
};

// 控制类型选项
const CONTROL_TYPE_OPTIONS = [
  { label: '启动', value: 'start' },
  { label: '停止', value: 'stop' },
  { label: '开启', value: 'open' },
  { label: '关闭', value: 'close' },
  { label: '调节', value: 'adjust' },
  { label: '重置', value: 'reset' }
];

export default defineComponent({
  name: 'ControlRecordDialog',
  components: {
    InfoFilled
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    deviceInfo: {
      type: Object,
      default: () => ({})
    },
    mode: {
      type: String,
      default: 'add' // add | edit
    },
    recordData: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:modelValue', 'submit', 'cancel'],
  setup(props, { emit }) {
    const formRef = ref<FormInstance>();
    const submitting = ref(false);
    const userStore = useUserStore();

    // 获取东八区当前时间的函数
    const getCurrentTimeInGMT8 = () => {
      const now = new Date();
      // 获取本地时间并格式化为 YYYY-MM-DD HH:mm:ss 格式
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    };

    const state = reactive({
      form: {
        controlType: '',
        controlValue: '',
        controlDescription: '',
        operatorName: userStore.name || userStore.firstName || '当前用户',
        controlTime: getCurrentTimeInGMT8(),

        remark: ''
      },
      controlKey: '', // 控制密钥单独存储，不作为表单数据
      formRules: {
        controlType: [
          { required: true, message: '请选择控制类型', trigger: 'change' }
        ],
        controlDescription: [
          { required: true, message: '请输入控制描述', trigger: 'blur' },
          { min: 5, max: 200, message: '控制描述长度在 5 到 200 个字符', trigger: 'blur' }
        ],
        controlTime: [
          { required: true, message: '请选择控制时间', trigger: 'change' }
        ]
      },
      controlKeyRules: [
        { required: true, message: '请输入控制密钥', trigger: 'blur' }
        // { min: 4, max: 50, message: '控制密钥长度在 4 到 10 个字符', trigger: 'blur' }
      ]
    });

    // 计算属性
    const visible = computed({
      get: () => props.modelValue,
      set: (value) => emit('update:modelValue', value)
    });

    const dialogTitle = computed(() => {
      return props.mode === 'edit' ? '编辑控制记录' : '新增控制记录';
    });

    const controlTypeOptions = computed(() => {
      // 根据设备类型过滤可用的控制类型
      const deviceTypeName = props.deviceInfo?.deviceTypeName;
      if (deviceTypeName === '水泵' || deviceTypeName?.includes('泵')) {
        return CONTROL_TYPE_OPTIONS.filter(option =>
          ['start', 'stop', 'reset'].includes(option.value)
        );
      } else if (deviceTypeName === '阀门' || deviceTypeName?.includes('阀')) {
        return CONTROL_TYPE_OPTIONS.filter(option =>
          ['open', 'close', 'adjust'].includes(option.value)
        );
      } else if (deviceTypeName === '监测设备' || deviceTypeName?.includes('监测')) {
        return CONTROL_TYPE_OPTIONS.filter(option =>
          ['start', 'stop', 'reset'].includes(option.value)
        );
      } else if (deviceTypeName === '控制器' || deviceTypeName?.includes('控制')) {
        return CONTROL_TYPE_OPTIONS;
      }
      return CONTROL_TYPE_OPTIONS;
    });

    const needControlValue = computed(() => {
      return state.form.controlType === 'adjust';
    });

    const controlValueUnit = computed(() => {
      const deviceTypeName = props.deviceInfo?.deviceTypeName;
      if (state.form.controlType === 'adjust') {
        if (deviceTypeName === '阀门' || deviceTypeName?.includes('阀')) return '%';
        if (deviceTypeName === '水泵' || deviceTypeName?.includes('泵')) return 'Hz';
      }
      return '';
    });

    const controlValueTip = computed(() => {
      if (state.form.controlType === 'adjust') {
        const deviceTypeName = props.deviceInfo?.deviceTypeName;
        if (deviceTypeName === '阀门' || deviceTypeName?.includes('阀')) return '阀门开度百分比，范围：0-100';
        if (deviceTypeName === '水泵' || deviceTypeName?.includes('泵')) return '水泵频率，范围：0-50Hz';
      }
      return '';
    });

    // 方法
    const getDeviceTypeName = (type: string) => {
      return DEVICE_TYPE_MAP[type] || type || '-';
    };

    const getDeviceStatusName = (status: number) => {
      return DEVICE_STATUS_MAP[status] || '未知';
    };

    const getDeviceStatusTagType = (status: number) => {
      const statusMap = {
        1: 'success',  // 在线
        0: 'info',     // 离线
        2: 'danger',   // 故障
        3: 'warning'   // 维护中
      };
      return statusMap[status] || 'info';
    };

    const resetForm = () => {
      state.form = {
        controlType: '',
        controlValue: '',
        controlDescription: '',
        operatorName: userStore.name || userStore.firstName || '当前用户',
        controlTime: getCurrentTimeInGMT8(),

        remark: ''
      };
      state.controlKey = ''; // 重置控制密钥
      formRef.value?.clearValidate();
    };

    const loadRecordData = () => {
      if (props.mode === 'edit' && props.recordData) {
        state.form = {
          controlType: props.recordData.controlType || '',
          controlValue: props.recordData.controlValue || '',
          controlDescription: props.recordData.controlDescription || '',
          operatorName: props.recordData.operatorName || '当前用户',
          controlTime: props.recordData.controlTime || '',

          remark: props.recordData.remark || ''
        };
      } else {
        resetForm();
      }
    };

    // 提取设备ID的辅助函数（与主页面保持一致）
    const extractDeviceId = (device: any): string => {
      if (typeof device?.id === 'object' && device.id?.id) {
        return device.id.id;
      }
      return device?.id || '';
    };

    const handleSubmit = async () => {
      if (!formRef.value) return;

      try {
        await formRef.value.validate();

        // 手动验证控制密钥
        if (!state.controlKey || state.controlKey.trim().length === 0) {
          ElMessage.error('请输入控制密钥');
          return;
        }
        // if (state.controlKey.length < 4 || state.controlKey.length > 10) {
        //   ElMessage.error('控制密钥长度在 4 到 10 个字符');
        //   return;
        // }

        submitting.value = true;

        const submitData = {
          ...state.form,
          controlKey: state.controlKey, // 添加控制密钥
          deviceId: extractDeviceId(props.deviceInfo),
          deviceName: props.deviceInfo?.name,
          deviceCode: extractDeviceId(props.deviceInfo),
          operatorId: 'current_user_id', // 实际应用中应该从用户信息中获取
          status: 'success' // 由于不实际下发命令，直接设置为成功
        };

        try {
          // 直接调用API保存控制记录
          await saveDeviceControlRecord(submitData);
          ElMessage.success('控制记录保存成功');
          emit('submit', submitData); // 通知父组件刷新数据
          visible.value = false; // 关闭对话框
        } catch (error: any) {
          // 处理提交错误
          console.log('捕获的错误:', error);
          console.log('错误类型:', typeof error);

          let errorMessage = '保存控制记录失败';

          // 由于axios拦截器已经处理了错误响应，这里的error可能直接就是错误信息字符串
          if (typeof error === 'string') {
            errorMessage = error;
          } else if (error?.response?.data?.message) {
            errorMessage = error.response.data.message;
          } else if (error?.message) {
            errorMessage = error.message;
          }

          console.log('最终错误信息:', errorMessage);
          ElMessage.error(errorMessage);
          return; // 阻止对话框关闭
        }
        
      } catch (error) {
        console.error('表单验证失败', error);
      } finally {
        submitting.value = false;
      }
    };

    const handleCancel = () => {
      emit('cancel');
      visible.value = false;
    };

    const handleClose = () => {
      emit('cancel');
    };

    // 监听对话框打开
    watch(visible, (newVal) => {
      if (newVal) {
        loadRecordData();
        // 对话框打开时，重新获取当前用户信息和设置时间
        state.form.operatorName = userStore.name || userStore.firstName || '当前用户';
        state.form.controlTime = getCurrentTimeInGMT8();
      }
    });

    // 监听控制类型变化，清空控制值
    watch(() => state.form.controlType, () => {
      if (!needControlValue.value) {
        state.form.controlValue = '';
      }
    });

    return {
      formRef,
      submitting,
      ...toRefs(state),
      visible,
      dialogTitle,
      controlTypeOptions,
      needControlValue,
      controlValueUnit,
      controlValueTip,
      extractDeviceId,
      getDeviceTypeName,
      getDeviceStatusName,
      getDeviceStatusTagType,
      handleSubmit,
      handleCancel,
      handleClose
    };
  }
});
</script>

<style scoped>
.device-info {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.device-info p {
  margin: 8px 0;
  color: #606266;
  font-size: 14px;
}

.device-info strong {
  color: #303133;
}

.form-tip {
  display: flex;
  align-items: center;
  margin-top: 5px;
  color: #909399;
  font-size: 12px;
}

.form-tip .el-icon {
  margin-right: 4px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
