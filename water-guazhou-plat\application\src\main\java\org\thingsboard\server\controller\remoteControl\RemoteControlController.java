package org.thingsboard.server.controller.remoteControl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.dto.remoteControl.DeviceControlRecordDto;
import org.thingsboard.server.dao.model.sql.remoteControl.DeviceControlRecordSetting;
import org.thingsboard.server.dao.model.sql.remoteControl.RemoteControlDeviceSetting;
import org.thingsboard.server.dao.remoteControl.RemoteControlService;
import org.thingsboard.server.dao.user.UserService;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 远程设备控制Controller
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Api(tags = "远程设备控制")
@Slf4j
@RestController
@RequestMapping("/api/remoteControl")
public class RemoteControlController extends BaseController {

    @Autowired
    private RemoteControlService remoteControlService;

    @Autowired
    private UserService userService;

    // ==================== 设备控制记录相关 ====================

    /**
     * 分页查询设备控制记录
     */
    @ApiOperation(value = "分页查询设备控制记录")
    @GetMapping("/records")
    public IstarResponse getControlRecordList(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String deviceId,
            @RequestParam(required = false) String deviceName,
            @RequestParam(required = false) String controlType,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String operatorName
    ) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        IPage<DeviceControlRecordSetting> result = remoteControlService.getControlRecordList(
                page, size, deviceId, deviceName, controlType, status, operatorName, tenantId
        );
        return IstarResponse.ok(result);
    }

    /**
     * 根据设备ID查询控制记录
     */
    @ApiOperation(value = "根据设备ID查询控制记录")
    @GetMapping("/records/{deviceId}")
    public IstarResponse getControlRecordsByDeviceId(@PathVariable String deviceId) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        List<DeviceControlRecordSetting> records = remoteControlService.getControlRecordsByDeviceId(deviceId, tenantId);
        return IstarResponse.ok(records);
    }

    /**
     * 保存设备控制记录
     */
    @ApiOperation(value = "保存设备控制记录")
    @PostMapping("/records")
    public IstarResponse saveControlRecord(@RequestBody DeviceControlRecordDto recordDto) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        String operatorId = UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId());

        // 验证控制密钥
        if (!validateControlKey(operatorId, recordDto.getControlKey())) {
            throw new RuntimeException("控制密钥验证失败，请检查密钥是否正确");
        }

        // 将DTO转换为实体
        DeviceControlRecordSetting record = new DeviceControlRecordSetting();
        record.setDeviceId(recordDto.getDeviceId());
        record.setDeviceName(recordDto.getDeviceName());
        record.setDeviceCode(recordDto.getDeviceCode());
        record.setControlType(recordDto.getControlType());
        record.setControlValue(recordDto.getControlValue());
        record.setControlDescription(recordDto.getControlDescription());
        record.setControlTime(recordDto.getControlTime());
        record.setRemark(recordDto.getRemark());
        record.setStatus(recordDto.getStatus());

        record.setTenantId(tenantId);
        record.setOperatorId(operatorId);
        record.setOperatorName(getCurrentUser().getName());

        // 设置时间
        Date currentTime = new Date();
        if (record.getControlTime() == null) {
            record.setControlTime(currentTime);
        }
        record.setCreateTime(currentTime);
        record.setUpdateTime(currentTime);

        if (record.getStatus() == null) {
            record.setStatus("pending");
        }

        DeviceControlRecordSetting savedRecord = remoteControlService.saveControlRecord(record);
        return IstarResponse.ok(savedRecord);
    }

    /**
     * 批量保存设备控制记录
     */
    @ApiOperation(value = "批量保存设备控制记录")
    @PostMapping("/records/batch")
    public IstarResponse batchSaveControlRecords(@RequestBody Map<String, Object> requestData) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        String operatorId = UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId());
        String operatorName = getCurrentUser().getName();

        // 从请求中获取控制密钥和记录列表
        String controlKey = (String) requestData.get("controlKey");
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> recordMaps = (List<Map<String, Object>>) requestData.get("records");

        // 验证控制密钥
        if (!validateControlKey(operatorId, controlKey)) {
            throw new RuntimeException("控制密钥验证失败，请检查密钥是否正确");
        }

        List<DeviceControlRecordSetting> records = new ArrayList<>();
        for (Map<String, Object> recordMap : recordMaps) {
            DeviceControlRecordSetting record = new DeviceControlRecordSetting();
            // 设置记录字段
            record.setDeviceId((String) recordMap.get("deviceId"));
            record.setDeviceName((String) recordMap.get("deviceName"));
            record.setDeviceCode((String) recordMap.get("deviceCode"));
            record.setControlType((String) recordMap.get("controlType"));
            record.setControlValue((String) recordMap.get("controlValue"));
            record.setControlDescription((String) recordMap.get("controlDescription"));
            record.setRemark((String) recordMap.get("remark"));
            record.setStatus((String) recordMap.get("status"));

            // 处理时间字段 - 如果没有提供时间，使用当前时间
            Object controlTimeObj = recordMap.get("controlTime");
            if (controlTimeObj != null && controlTimeObj instanceof Date) {
                record.setControlTime((Date) controlTimeObj);
            } else {
                record.setControlTime(new Date());
            }

            // 设置通用字段
            record.setTenantId(tenantId);
            record.setOperatorId(operatorId);
            record.setOperatorName(operatorName);

            // 设置默认状态
            if (record.getStatus() == null || record.getStatus().isEmpty()) {
                record.setStatus("pending");
            }

            records.add(record);
        }

        List<DeviceControlRecordSetting> savedRecords = remoteControlService.batchSaveControlRecords(records);
        return IstarResponse.ok(savedRecords);
    }

    /**
     * 删除设备控制记录
     */
    @ApiOperation(value = "删除设备控制记录")
    @DeleteMapping("/records/{recordId}")
    public IstarResponse deleteControlRecord(@PathVariable String recordId) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        boolean result = remoteControlService.deleteControlRecord(recordId, tenantId);
        return result ? IstarResponse.ok("删除成功") : IstarResponse.error("删除失败");
    }

    /**
     * 更新控制记录状态
     */
    @ApiOperation(value = "更新控制记录状态")
    @PutMapping("/records/{recordId}/status")
    public IstarResponse updateControlRecordStatus(
            @PathVariable String recordId,
            @RequestParam String status
    ) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        boolean result = remoteControlService.updateControlRecordStatus(recordId, status, tenantId);
        return result ? IstarResponse.ok("更新成功") : IstarResponse.error("更新失败");
    }

    // ==================== 远程控制设备相关 ====================

    /**
     * 分页查询远程控制设备列表
     */
    @ApiOperation(value = "分页查询远程控制设备列表")
    @GetMapping("/device/list")
    public IstarResponse getRemoteControlDeviceList(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String waterPlantId,
            @RequestParam(required = false) String deviceName,
            @RequestParam(required = false) String deviceCode,
            @RequestParam(required = false) String deviceType,
            @RequestParam(required = false) String status
    ) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        IPage<RemoteControlDeviceSetting> result = remoteControlService.getRemoteControlDeviceList(
                page, size, waterPlantId, deviceType, null, tenantId
        );
        return IstarResponse.ok(result);
    }

    /**
     * 根据设备ID查询设备详情
     */
    @ApiOperation(value = "根据设备ID查询设备详情")
    @GetMapping("/device/{deviceId}")
    public IstarResponse getRemoteControlDeviceById(@PathVariable String deviceId) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        RemoteControlDeviceSetting device = remoteControlService.getRemoteControlDeviceById(deviceId, tenantId);
        return IstarResponse.ok(device);
    }

    /**
     * 根据水厂ID查询设备列表
     */
    @ApiOperation(value = "根据水厂ID查询设备列表")
    @GetMapping("/device/waterPlant/{waterPlantId}")
    public IstarResponse getDevicesByWaterPlantId(@PathVariable String waterPlantId) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        List<RemoteControlDeviceSetting> devices = remoteControlService.getDevicesByWaterPlantId(waterPlantId, tenantId);
        return IstarResponse.ok(devices);
    }

    // ==================== 统计相关 ====================

    /**
     * 获取设备类型统计
     */
    @ApiOperation(value = "获取设备类型统计")
    @GetMapping("/device/stats/type")
    public IstarResponse getDeviceTypeStats() throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        List<Map<String, Object>> stats = remoteControlService.getDeviceTypeStats(tenantId);
        return IstarResponse.ok(stats);
    }

    /**
     * 获取控制记录统计
     */
    @ApiOperation(value = "获取控制记录统计")
    @GetMapping("/records/stats")
    public IstarResponse getControlRecordStats() throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        Map<String, Object> stats = remoteControlService.getControlRecordStats(tenantId);
        return IstarResponse.ok(stats);
    }

    /**
     * 验证控制密钥
     *
     * @param operatorId 操作员ID
     * @param controlKey 控制密钥
     * @return 验证结果
     */
    private boolean validateControlKey(String operatorId, String controlKey) {
        try {
            // 检查密钥是否为空
            if (controlKey == null || controlKey.trim().isEmpty()) {
                return false;
            }

            // 检查操作员ID是否为空
            if (operatorId == null || operatorId.trim().isEmpty()) {
                return false;
            }

            // 调用userService进行密钥验证
            Boolean result = userService.validateControlKey(operatorId, controlKey);

            // 返回验证结果，如果result为null则返回false
            return result != null && result;

        } catch (Exception e) {
            // 记录异常日志
            log.error("控制密钥验证异常，操作员ID: {}", operatorId, e);
            return false;
        }
    }
}
